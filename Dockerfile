# Production runtime image - no build stage needed
FROM oven/bun:1.2.19
WORKDIR /app

# Install OpenSSL for runtime
RUN apt-get update -y && apt-get install -y openssl && rm -rf /var/lib/apt/lists/*

# Copy package files for runtime dependencies
COPY package.json bun.lock ./
COPY client/package.json ./client/
COPY server/package.json ./server/
COPY shared/package.json ./shared/

# Install only production dependencies
RUN bun install --frozen-lockfile --production --ignore-scripts

# Copy pre-built application files (build locally before Docker)
COPY server/dist ./server/dist
COPY server/static ./server/static
COPY server/prisma ./server/prisma
COPY shared/dist ./shared/dist

# Copy pre-generated Prisma client (generated locally)
COPY node_modules/@prisma/client ./node_modules/@prisma/client
COPY node_modules/.prisma ./node_modules/.prisma

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

EXPOSE 3000
CMD ["bun", "run", "start:single"]
