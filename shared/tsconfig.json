{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    // Environment setup
    "lib": ["ESNext"],
    "target": "ESNext",
    "module": "ESNext",

    // Output configuration
    "declaration": true,  // Enable .d.ts generation
    "outDir": "./dist",
    "rootDir": "./src",
    "noEmit": false,  // Enable file emission

    // Type checking
    "strict": true,
    "skipLibCheck": true,

    // Additional checks
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "**/*.test.ts", "dist"]
}
