export enum ModuleType {
  DATA_ESEAL = "DATA_ESEAL",
  TRACKING_DATA = "TRACKING_DATA",
  LOGS = "LOGS",
  PENGATURAN_SISTEM = "PENGATURAN_SISTEM",
}

export type ApiResponse = {
  message: string;
  success: true;
}

export type ESealData = {
  id: number;
  perusahaan: string;
  idVendor: string;
  merk: string;
  model: string;
  nomorIMEI: string;
  tipeESeal: string;
  token: string;
  jarakTempuh: string;
  status: 'Unlocked' | 'Locked';
}

export type ESealResponse = {
  data: ESealData[];
  total: number;
  page: number;
  limit: number;
}

// Beacukai API Types
export interface BeacukaiTrackingStartRequest {
  alamatAsal: string;
  alamatTujuan: string;
  idVendor: string;
  jnsKontainer: string;
  latitudeAsal: string;
  latitudeTujuan: string;
  lokasiAsal: string;
  lokasiTujuan: string;
  longitudeAsal: string;
  longitudeTujuan: string;
  noImei: string;
  noEseal: string;
  noKontainer: string;
  noPolisi: string;
  token: string;
  ukKontainer: string;
  namaDriver: string;
  nomorTeleponDriver: string;
  dokumen: Array<{
    jenisMuat: string;
    jumlahKontainer: string;
    kodeDokumen: string;
    kodeKantor: string;
    nomorAju: string;
    nomorDokumen: string;
    tanggalDokumen: string;
  }>;
}

export interface BeacukaiApiResponse {
  status: 'success' | 'error';
  message: string;
  item?: any;
}

// Setup E-Seal Form Data
export interface SetupESealFormData {
  // Step 1 - Data E-Seal (mapped to API fields)
  organizationId: string;    // NEW - required for assigning E-Seal
  alamatAsal: string;        // alamatESeal → alamatAsal
  alamatTujuan: string;      // alamatTujuan → alamatTujuan
  idVendor: string;          // idVendor → idVendor
  noEseal: string;           // nomorESeal → noEseal
  noImei: string;            // nomorIMEI → noImei
  token: string;             // token → token
  merk: string;              // merk E-Seal
  model: string;             // model E-Seal
  tipe: string;              // tipe E-Seal

  // Step 2 - Lokasi (new fields for API)
  lokasiAsal: string;        // lokasiAsal → lokasiAsal
  lokasiTujuan: string;      // lokasiTujuan → lokasiTujuan
  latitudeAsal: string;      // NEW - required by API
  longitudeAsal: string;     // NEW - required by API
  latitudeTujuan: string;    // NEW - required by API
  longitudeTujuan: string;   // NEW - required by API

  // Step 3 - Kendaraan & Driver (mapped to API fields)
  noPolisi: string;          // nomorPolisi → noPolisi
  ukKontainer: string;       // ukuranKontainer → ukKontainer
  jnsKontainer: string;      // jenisKontainer → jnsKontainer
  noKontainer: string;       // nomorKontainer → noKontainer
  namaDriver: string;        // namaDriver → namaDriver
  nomorTeleponDriver: string; // noTeleponDriver → nomorTeleponDriver

  // Step 4 - Validasi AJU & Dokumen
  nomorAJU: string;
  statusValidasi: 'pending' | 'validating' | 'valid' | 'invalid';
  dokumen: Array<{
    jenisMuat: string;
    jumlahKontainer: string;
    kodeDokumen: string;
    kodeKantor: string;
    nomorAju: string;
    nomorDokumen: string;
    tanggalDokumen: string;
  }>;
}
