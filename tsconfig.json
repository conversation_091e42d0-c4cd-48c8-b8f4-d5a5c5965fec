{
  "compilerOptions": {
    // Environment setup & latest features
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "target": "ESNext",
    "module": "ESNext",
    "moduleDetection": "force",
    "jsx": "react-jsx",
    "allowJs": true,

    // Path resolution
    "baseUrl": "./",
    "paths": {
      "@server/*": ["./server/src/*"],
      "@client/*": ["./client/src/*"],
      "@shared/*": ["./shared/src/*"]
    },

    // Module resolution
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "verbatimModuleSyntax": true,

    // Strictness and best practices
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "experimentalDecorators": true,

    // Output control
    "skipLibCheck": true,

    // Optional strict flags (disabled by default)
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noPropertyAccessFromIndexSignature": false
  },
  "include": ["server/src", "shared/src"],
  "exclude": ["node_modules", "dist", "client/dist", "server/dist", "shared/dist", "**/*.js"]
}
