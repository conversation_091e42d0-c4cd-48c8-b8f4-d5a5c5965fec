{"name": "E-Seal Mock API", "nodes": [{"parameters": {"httpMethod": "POST", "path": "eseal/add", "responseMode": "responseNode", "options": {}}, "id": "eseal-add-webhook", "name": "Mock E-Seal Add", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 200]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil menambahkan data ke tabel td_eseal\",\n  \"item\": {\n    \"idEseal\": \"mock-eseal-id\",\n    \"merk\": \"{{$json.body.merk}}\",\n    \"model\": \"{{$json.body.model}}\",\n    \"tipe\": \"{{$json.body.tipe}}\",\n    \"idVendor\": \"{{$json.body.idVendor}}\",\n    \"noImei\": \"{{$json.body.noImei}}\",\n    \"status\": \"1\",\n    \"wkRekam\": \"{{$now.toMillis()}}\",\n    \"wkUpdate\": \"{{$now.toMillis()}}\"\n  }\n}"}, "id": "eseal-add-response", "name": "E-Seal Add Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 200]}, {"parameters": {"httpMethod": "GET", "path": "eseal/get-dok-pabean", "responseMode": "responseNode", "options": {}}, "id": "get-dok-pabean-webhook", "name": "<PERSON><PERSON> Get Dok <PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil mendapatkan dokumen pabean\",\n  \"item\": {\n    \"nomorAju\": \"{{$json.query.nomor_aju}}\",\n    \"kodeDokumen\": \"20\",\n    \"nomorDaftar\": \"000123\",\n    \"tanggalDaftar\": \"2024-01-01\",\n    \"kodeKantor\": \"050100\",\n    \"namaKantor\": \"KPU Bea dan Cukai Tipe A Tanjung Priok\",\n    \"kodeTps\": \"KOJA\",\n    \"namaGudang\": \"Gudang KOJA\",\n    \"idPengusaha\": \"123456789012345\",\n    \"namaPengusaha\": \"PT. IMPORTIR SEJAHTERA\",\n    \"uraian\": \"BARANG IMPOR\",\n    \"kontainer\": [\n      {\n        \"nomorKontainer\": \"DRYU9876543\",\n        \"nomorSegel\": \"BC-123456\"\n      }\n    ]\n  }\n}"}, "id": "get-dok-pabean-response", "name": "Get Dok Pabean Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"httpMethod": "POST", "path": "tracking/start", "responseMode": "responseNode", "options": {}}, "id": "tracking-start-webhook", "name": "Mock Tracking Start", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 400]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil memulai tracking\",\n  \"item\": {\n    \"trackingId\": \"track-{{$now.toMillis()}}\"\n  }\n}"}, "id": "tracking-start-response", "name": "Tracking Start Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 400]}, {"parameters": {"httpMethod": "POST", "path": "eseal/update-position", "responseMode": "responseNode", "options": {}}, "id": "update-position-webhook", "name": "Mock Update Position", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 500]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil menerima data posisi\"\n}"}, "id": "update-position-response", "name": "Update Position Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 500]}, {"parameters": {"httpMethod": "POST", "path": "tracking/stop", "responseMode": "responseNode", "options": {}}, "id": "tracking-stop-webhook", "name": "Mock Tracking Stop", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 600]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil menghentikan tracking\",\n  \"item\": {\n    \"trackingId\": \"track-{{$now.toMillis()}}\"\n  }\n}"}, "id": "tracking-stop-response", "name": "Tracking Stop Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 600]}, {"parameters": {"httpMethod": "POST", "path": "eseal/updatestatus-device", "responseMode": "responseNode", "options": {}}, "id": "update-status-device-webhook", "name": "Mock Update Status Device", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 700]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil mengubah data di tabel td_eseal\"\n}"}, "id": "update-status-device-response", "name": "Update Status Device Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 700]}, {"parameters": {"httpMethod": "GET", "path": "tracking/status", "responseMode": "responseNode", "options": {}}, "id": "tracking-status-webhook", "name": "Mock Tracking Status", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 800]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil mendapatkan data tracking untuk nomor eseal {{$json.query.noEseal}}\",\n  \"item\": {\n    \"start\": \"success\",\n    \"updatePosition\": 10,\n    \"stop\": \"pending\"\n  }\n}"}, "id": "tracking-status-response", "name": "Tracking Status Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 800]}, {"parameters": {"httpMethod": "POST", "path": "device/list_all", "responseMode": "responseNode", "options": {}}, "id": "device-list-all-webhook", "name": "<PERSON><PERSON>ce List All", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 900]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\": 200,\n  \"data\": [\n    {\n      \"deviceId\": \"device-001\",\n      \"vehicleId\": 1,\n      \"vehicleName\": \"Truck A\",\n      \"online\": true,\n      \"lat\": -6.175392,\n      \"lng\": 106.827153,\n      \"speed\": 50,\n      \"devBatteryPCT\": 85,\n      \"lockStatus\": 1\n    },\n    {\n      \"deviceId\": \"device-002\",\n      \"vehicleId\": 2,\n      \"vehicleName\": \"Truck B\",\n      \"online\": false,\n      \"lat\": -6.208763,\n      \"lng\": 106.845599,\n      \"speed\": 0,\n      \"devBatteryPCT\": 50,\n      \"lockStatus\": 0\n    }\n  ],\n  \"message\": \"Success\"\n}"}, "id": "device-list-all-response", "name": "Device List All Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 900]}], "connections": {"eseal-add-webhook": {"main": [[{"node": "eseal-add-response", "type": "main", "index": 0}]]}, "get-dok-pabean-webhook": {"main": [[{"node": "get-dok-pabean-response", "type": "main", "index": 0}]]}, "tracking-start-webhook": {"main": [[{"node": "tracking-start-response", "type": "main", "index": 0}]]}, "update-position-webhook": {"main": [[{"node": "update-position-response", "type": "main", "index": 0}]]}, "tracking-stop-webhook": {"main": [[{"node": "tracking-stop-response", "type": "main", "index": 0}]]}, "update-status-device-webhook": {"main": [[{"node": "update-status-device-response", "type": "main", "index": 0}]]}, "tracking-status-webhook": {"main": [[{"node": "tracking-status-response", "type": "main", "index": 0}]]}, "device-list-all-webhook": {"main": [[{"node": "device-list-all-response", "type": "main", "index": 0}]]}}, "pinData": {}}