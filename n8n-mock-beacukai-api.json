{"name": "e-Seal <PERSON><PERSON>", "active": false, "id": "e-seal-beacukai-mock-api-v1", "createdAt": "2024-10-26T00:00:00.000Z", "updatedAt": "2024-10-26T00:00:00.000Z", "versionId": "e-seal-beacukai-mock-api-v1", "triggerCount": 0, "tags": ["mock", "api", "<PERSON><PERSON><PERSON><PERSON>", "eseal"], "settings": {}, "staticData": null, "meta": {"template": false}, "pinData": {}, "timeZone": "Asia/Jakarta", "workflowApiVersion": "1.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "eseal/create", "responseMode": "responseNode", "options": {}}, "id": "esealCreateWebhook", "name": "Webhook - Eseal Create", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 100], "webhookId": "eseal-create"}, {"parameters": {"functionCode": "return [\n  {\n    json: {\n      status: \"success\",\n      message: \"Ber<PERSON>il menambahkan data ke tabel td_eseal\",\n      item: {\n        idEseal: \"mock-eseal-1\",\n        merk: \"MockMerk\",\n        model: \"MockModel\",\n        tipe: \"MockTipe\",\n        idVendor: \"MockVendorID\",\n        noImei: \"MockIMEI\",\n        status: \"INACTIVE\",\n        wkRekam: \"2024-10-26T00:00:00.000Z\",\n        wkUpdate: \"2024-10-26T00:00:00.000Z\"\n      }\n    }\n  }\n];"}, "name": "Function - Create Eseal", "type": "n8n-nodes-base.function", "typeVersion": 1, "id": "createEsealFunction", "position": [500, 100]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "esealCreateResponse", "name": "Respond - Eseal Create", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [750, 100]}, {"parameters": {"httpMethod": "GET", "path": "eseal/get-dok-pabean", "responseMode": "responseNode", "options": {}}, "id": "getDokPabeanWebhook", "name": "Webhook - Get Dok Pabean", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 250], "webhookId": "get-dok-pabean"}, {"parameters": {"functionCode": "return [\n  {\n    json: {\n      status: \"success\",\n      message: \"<PERSON><PERSON><PERSON>il mendapatkan dokumen pabean\",\n      item: {\n        nomorAju: \"16021600008120160415000002\",\n        kodeDokumen: \"PLP\",\n        nomorDaftar: \"12345\",\n        tanggalDaftar: \"2024-10-26\",\n        kodeKantor: \"050100\",\n        namaKantor: \"KANTOR BEA CUKAI\",\n        kodeTps: \"TPS01\",\n        namaGudang: \"GUDANG ABC\",\n        idPengusaha: \"VENDOR123\",\n        namaPengusaha: \"PT. MOCK VENDOR\",\n        uraian: \"<PERSON><PERSON> Umum\",\n        kontainer: [\n          {\n            nomorKontainer: \"CONT12345\",\n            nomorSegel: \"ESEALTEST123098\"\n          }\n        ]\n      }\n    }\n  }\n];"}, "name": "Function - Get Dok Pabean", "type": "n8n-nodes-base.function", "typeVersion": 1, "id": "getDokPabeanFunction", "position": [500, 250]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "getDokPabeanResponse", "name": "Respond - Get Dok Pabean", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [750, 250]}, {"parameters": {"httpMethod": "POST", "path": "tracking/start", "responseMode": "responseNode", "options": {}}, "id": "trackingStartWebhook", "name": "Webhook - Tracking Start", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 400], "webhookId": "tracking-start"}, {"parameters": {"functionCode": "return [\n  {\n    json: {\n      status: \"success\",\n      message: \"Berhasil menambahkan data tracking\",\n      item: {\n        id: \"mock-tracking-1\",\n        status: \"started\",\n        wkRekam: \"2024-10-26T00:00:00.000Z\"\n      }\n    }\n  }\n];"}, "name": "Function - Tracking Start", "type": "n8n-nodes-base.function", "typeVersion": 1, "id": "trackingStartFunction", "position": [500, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "trackingStartResponse", "name": "Respond - Tracking Start", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [750, 400]}, {"parameters": {"httpMethod": "POST", "path": "tracking/stop", "responseMode": "responseNode", "options": {}}, "id": "trackingStopWebhook", "name": "Webhook - Tracking Stop", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 550], "webhookId": "tracking-stop"}, {"parameters": {"functionCode": "return [\n  {\n    json: {\n      status: \"success\",\n      message: \"Berhasil menghentikan data tracking\",\n      item: {\n        noEseal: \"ESEALTEST123098\",\n        status: \"stopped\",\n        wkUpdate: \"2024-10-26T00:00:00.000Z\"\n      }\n    }\n  }\n];"}, "name": "Function - Tracking Stop", "type": "n8n-nodes-base.function", "typeVersion": 1, "id": "trackingStopFunction", "position": [500, 550]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "trackingStopResponse", "name": "Respond - Tracking Stop", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [750, 550]}, {"parameters": {"httpMethod": "POST", "path": "eseal/update-position", "responseMode": "responseNode", "options": {}}, "id": "updatePositionWebhook", "name": "Webhook - Update Position", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 700], "webhookId": "update-position"}, {"parameters": {"functionCode": "return [\n  {\n    json: {\n      status: \"success\",\n      message: \"Berhasil menerima data posisi\"\n    }\n  }\n];"}, "name": "Function - Update Position", "type": "n8n-nodes-base.function", "typeVersion": 1, "id": "updatePositionFunction", "position": [500, 700]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "updatePositionResponse", "name": "Respond - Update Position", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [750, 700]}, {"parameters": {"httpMethod": "POST", "path": "eseal/update-status-device", "responseMode": "responseNode", "options": {}}, "id": "updateStatusDeviceWebhook", "name": "Webhook - Update Status Device", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 850], "webhookId": "update-status-device"}, {"parameters": {"functionCode": "return [\n  {\n    json: {\n      status: \"success\",\n      message: \"Be<PERSON><PERSON>il mengubah data di tabel td_eseal\"\n    }\n  }\n];"}, "name": "Function - Update Status Device", "type": "n8n-nodes-base.function", "typeVersion": 1, "id": "updateStatusDeviceFunction", "position": [500, 850]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "updateStatusDeviceResponse", "name": "Respond - Update Status Device", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [750, 850]}, {"parameters": {"httpMethod": "GET", "path": "tracking/status", "responseMode": "responseNode", "options": {}}, "id": "trackingStatusWebhook", "name": "Webhook - Tracking Status", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 1000], "webhookId": "tracking-status"}, {"parameters": {"functionCode": "return [\n  {\n    json: {\n      status: \"success\",\n      message: \"Berhasil mendapatkan data tracking untuk nomor eseal ESEALTEST-123098\",\n      item: {\n        start: \"success\",\n        updatePosition: 0,\n        stop: \"success\"\n      }\n    }\n  }\n];"}, "name": "Function - Tracking Status", "type": "n8n-nodes-base.function", "typeVersion": 1, "id": "trackingStatusFunction", "position": [500, 1000]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "trackingStatusResponse", "name": "Respond - Tracking Status", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [750, 1000]}], "connections": {"Webhook - Eseal Create": {"main": [[{"node": "Function - Create Eseal", "type": "main", "index": 0}]]}, "Function - Create Eseal": {"main": [[{"node": "Respond - Eseal Create", "type": "main", "index": 0}]]}, "Webhook - Get Dok Pabean": {"main": [[{"node": "Function - Get Dok Pabean", "type": "main", "index": 0}]]}, "Function - Get Dok Pabean": {"main": [[{"node": "Respond - Get Dok Pabean", "type": "main", "index": 0}]]}, "Webhook - Tracking Start": {"main": [[{"node": "Function - Tracking Start", "type": "main", "index": 0}]]}, "Function - Tracking Start": {"main": [[{"node": "Respond - Tracking Start", "type": "main", "index": 0}]]}, "Webhook - Tracking Stop": {"main": [[{"node": "Function - Tracking Stop", "type": "main", "index": 0}]]}, "Function - Tracking Stop": {"main": [[{"node": "Respond - Tracking Stop", "type": "main", "index": 0}]]}, "Webhook - Update Position": {"main": [[{"node": "Function - Update Position", "type": "main", "index": 0}]]}, "Function - Update Position": {"main": [[{"node": "Respond - Update Position", "type": "main", "index": 0}]]}, "Webhook - Update Status Device": {"main": [[{"node": "Function - Update Status Device", "type": "main", "index": 0}]]}, "Function - Update Status Device": {"main": [[{"node": "Respond - Update Status Device", "type": "main", "index": 0}]]}, "Webhook - Tracking Status": {"main": [[{"node": "Function - Tracking Status", "type": "main", "index": 0}]]}, "Function - Tracking Status": {"main": [[{"node": "Respond - Tracking Status", "type": "main", "index": 0}]]}}}