import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Eye, EyeOff, Mail, Lock, User } from 'lucide-react';
import { signUp } from '@/lib/auth-client';

export default function Register() {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    if (formData.password !== formData.confirmPassword) {
      alert('Password dan konfirmasi password tidak sama');
      setIsLoading(false);
      return;
    }

    try {
      const result = await signUp.email({
        email: formData.email,
        password: formData.password,
        name: formData.name,
      });

      if (result.error) {
        console.error('Register error:', result.error);
        alert('Registrasi gagal: ' + result.error.message);
      } else {
        alert('Registrasi berhasil! Silakan login.');
        navigate('/login');
      }
    } catch (error) {
      console.error('Register error:', error);
      alert('Registrasi gagal. Silakan coba lagi.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center p-4">
      {/* Outer Floating Container - White Background */}
      <div className="w-full max-w-5xl bg-white rounded-3xl shadow-2xl p-8 lg:p-12">
        <div className="flex flex-col lg:flex-row items-center justify-center min-h-[500px] gap-8 lg:gap-12">

          {/* Left Side - Navy Register Card */}
          <div className="w-full max-w-md">
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl shadow-2xl p-8">
              {/* Logo Section */}
              <div className="text-center mb-8">
                <div className="inline-block border-2 border-white rounded-lg p-3 mb-4">
                  <div className="text-white">
                    <div className="text-lg font-bold">
                      V<sup className="text-xs">th</sup> <span className="text-blue-400">VIT Plus</span>
                    </div>
                    <div className="text-xs mt-1 border-t border-white pt-1">
                      Secure, Reliable & Accurate
                    </div>
                  </div>
                </div>
              </div>

              {/* Welcome Message */}
              <div className="text-center mb-8">
                <h1 className="text-2xl font-bold text-white mb-4">
                  Daftar Akun Baru
                </h1>
                <div className="w-16 h-0.5 bg-white mx-auto"></div>
              </div>

              {/* Register Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name Input */}
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    placeholder="Nama Lengkap"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="pl-10 h-12 bg-transparent border-2 border-gray-400 text-white placeholder-gray-400 focus:border-white focus:ring-0 rounded-lg"
                    required
                  />
                </div>

                {/* Email Input */}
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="Email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="pl-10 h-12 bg-transparent border-2 border-gray-400 text-white placeholder-gray-400 focus:border-white focus:ring-0 rounded-lg"
                    required
                  />
                </div>

                {/* Password Input */}
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Kata sandi"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="pl-10 pr-10 h-12 bg-transparent border-2 border-gray-400 text-white placeholder-gray-400 focus:border-white focus:ring-0 rounded-lg"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                  >
                    {showPassword ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button>
                </div>

                {/* Confirm Password Input */}
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="Konfirmasi kata sandi"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="pl-10 pr-10 h-12 bg-transparent border-2 border-gray-400 text-white placeholder-gray-400 focus:border-white focus:ring-0 rounded-lg"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button>
                </div>

                {/* Login Link */}
                <div className="text-left">
                  <button
                    type="button"
                    onClick={() => navigate('/login')}
                    className="text-sm text-gray-300 hover:text-white transition-colors"
                  >
                    Sudah punya akun? Login di sini
                  </button>
                </div>

                {/* Register Button */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-12 bg-white text-slate-800 hover:bg-gray-100 font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-slate-800 border-t-transparent rounded-full animate-spin"></div>
                      <span>Mendaftar...</span>
                    </div>
                  ) : (
                    'Daftar'
                  )}
                </Button>
              </form>
            </div>
          </div>

          {/* Right Side - Illustration */}
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center max-w-md">
              {/* Illustration Area */}
              <div className="relative mb-8">
                <div className="w-80 h-80 mx-auto relative flex items-center justify-center">
                  <img
                    src="/in-sync/amico.png"
                    alt="Register Illustration"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
