import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Input } from '../../../components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Search } from 'lucide-react';
import MapTiler from '../../../components/map/MapTiler';
import { useESealData } from '../../../hooks/useESealData';

// Dummy data untuk monitoring sesuai spesifikasi
const dummyMonitoringData = [
  {
    id: '1',
    no: 1,
    icon: '📍',
    noESeal: '7848585252',
    noIMEI: '875298572967967922',
    address: 'Jalan Pantura, Kendal, Jawa Tengah',
    posisiAltitude: '636373737',
    dayaBaterai: '15000',
    dayaAki: '350000',
    event: 'UNLOCKED',
    idVendor: '884CKEL637',
    kecepatanKontainer: '500',
    posisiLatitude: '-6.2627272.77',
    posisiLongitude: '27.26262.14166',
    provinsi: 'Jawa Tengah',
    kota: 'Kendal',
    token: '123567888',
    status: 'ONLINE'
  }
];

const Monitoring: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [search, setSearch] = useState('');
  const [selectedEseals, setSelectedEseals] = useState<string[]>([]);

  const { data: esealData } = useESealData({
    page: 1,
    limit: 1000,
    organizationSlug: slug,
    search,
  });

  const handleSelectEseal = (id: string) => {
    setSelectedEseals(prev =>
      prev.includes(id) ? prev.filter(esealId => esealId !== id) : [...prev, id]
    );
  };

  const monitoredEseals = esealData?.filter(e => selectedEseals.includes(e.id)) || [];
  const staticLocation = { lat: -6.2088, lng: 106.8456, address: 'Jakarta, Indonesia' };

  return (
    <div className="space-y-6 h-full flex flex-col">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Monitoring</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 flex-grow">
        {/* Left Panel */}
        <div className="lg:col-span-1 bg-white p-4 rounded-lg border flex flex-col space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input placeholder="Cari E-Seal..." className="pl-10" value={search} onChange={e => setSearch(e.target.value)} />
          </div>
          <div className="flex-grow space-y-2 overflow-y-auto">
            {esealData?.map(eseal => (
              <div key={eseal.id} className="flex items-center space-x-2">
                <input type="checkbox" id={eseal.id} checked={selectedEseals.includes(eseal.id)} onChange={() => handleSelectEseal(eseal.id)} />
                <label htmlFor={eseal.id} className="text-sm">{eseal.noEseal}</label>
                <span className={`text-xs ${eseal.status === 'ACTIVE' ? 'text-green-600' : 'text-red-600'}`}>
                  {eseal.status === 'ACTIVE' ? 'ONLINE' : 'OFFLINE'}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Right Panel - Map */}
        <div className="lg:col-span-2 bg-white p-4 rounded-lg border">
          <MapTiler originLocation={staticLocation} interactive={false} />
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">No</TableHead>
                <TableHead className="font-semibold text-white">Icon</TableHead>
                <TableHead className="font-semibold text-white">No E-Seal</TableHead>
                <TableHead className="font-semibold text-white">No IMEI</TableHead>
                <TableHead className="font-semibold text-white">Address</TableHead>
                <TableHead className="font-semibold text-white">Posisi Altitude</TableHead>
                <TableHead className="font-semibold text-white">Daya Baterai</TableHead>
                <TableHead className="font-semibold text-white">Daya Aki</TableHead>
                <TableHead className="font-semibold text-white">Event</TableHead>
                <TableHead className="font-semibold text-white">ID Vendor</TableHead>
                <TableHead className="font-semibold text-white">Kecepatan Kontainer</TableHead>
                <TableHead className="font-semibold text-white">Posisi Latitude</TableHead>
                <TableHead className="font-semibold text-white">Posisi Longitude</TableHead>
                <TableHead className="font-semibold text-white">Provinsi</TableHead>
                <TableHead className="font-semibold text-white">Kota</TableHead>
                <TableHead className="font-semibold text-white">Token</TableHead>
                <TableHead className="font-semibold text-white">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {dummyMonitoringData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={17} className="text-center py-8 text-gray-500">
                    Pilih E-Seal untuk dimonitor.
                  </TableCell>
                </TableRow>
              ) : (
                dummyMonitoringData.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{item.no}</TableCell>
                    <TableCell className="text-center text-lg">{item.icon}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noESeal}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noIMEI}</TableCell>
                    <TableCell className="text-sm">{item.address}</TableCell>
                    <TableCell className="font-mono text-sm">{item.posisiAltitude}</TableCell>
                    <TableCell className="font-mono text-sm">{item.dayaBaterai}</TableCell>
                    <TableCell className="font-mono text-sm">{item.dayaAki}</TableCell>
                    <TableCell className="text-sm">{item.event}</TableCell>
                    <TableCell className="font-mono text-sm">{item.idVendor}</TableCell>
                    <TableCell className="font-mono text-sm">{item.kecepatanKontainer}</TableCell>
                    <TableCell className="font-mono text-sm">{item.posisiLatitude}</TableCell>
                    <TableCell className="font-mono text-sm">{item.posisiLongitude}</TableCell>
                    <TableCell className="text-sm">{item.provinsi}</TableCell>
                    <TableCell className="text-sm">{item.kota}</TableCell>
                    <TableCell className="font-mono text-sm">{item.token}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        item.status === 'ONLINE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {item.status}
                      </span>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default Monitoring;
