import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Search, Loader2 } from 'lucide-react';

interface DokumenPabean {
  nomorAju: string;
  kodeDokumen: string;
  nomorDaftar: string;
  tanggalDaftar: string;
  kodeKantor: string;
  namaKantor: string;
  kodeTps: string;
  namaGudang: string;
  idPengusha: string;
  namaPengusaha: string;
  uraian: string;
  nomorKontainer: string;
  nomorSegel: string;
}

// Dummy data untuk dokumen kepabean sesuai spesifikasi
const dummyDokumenData: DokumenPabean = {
  nomorAju: 'AJU123456789',
  kodeDokumen: 'DOK001',
  nomorDaftar: 'DF001',
  tanggalDaftar: '25 Jan 2025',
  kodeKantor: 'KNT001',
  namaKantor: 'Kantor Bea Cukai Jakarta',
  kodeTps: 'TPS001',
  namaGudang: 'Gudang Utama',
  idPengusha: 'PGS001',
  namaPengusaha: 'PT. Ekspor Jaya',
  uraian: 'Ekspor Barang Elektronik',
  nomorKontainer: 'CONT123456',
  nomorSegel: 'SGL001'
};

const DokumenKepabean: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [nomorAju, setNomorAju] = useState('');
  const [dokumen, setDokumen] = useState<DokumenPabean | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFetchData = async () => {
    if (!nomorAju) {
      setError('Nomor Aju tidak boleh kosong.');
      return;
    }
    setLoading(true);
    setError(null);
    setDokumen(null);

    try {
      // Simulasi API call dengan dummy data
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (nomorAju === dummyDokumenData.nomorAju) {
        setDokumen(dummyDokumenData);
      } else {
        throw new Error('Data dokumen tidak ditemukan.');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Dokumen Kepabean</h1>
      </div>

      <div className="flex items-center gap-3 bg-white p-4 rounded-lg border">
        <label htmlFor="nomorAju" className="text-sm font-medium text-gray-700">
          Masukkan Nomor Aju
        </label>
        <div className="relative flex-grow max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            id="nomorAju"
            placeholder="Nomor Aju"
            className="pl-10"
            value={nomorAju}
            onChange={(e) => setNomorAju(e.target.value)}
          />
        </div>
        <Button onClick={handleFetchData} disabled={loading} className="bg-slate-800 hover:bg-slate-700 text-white">
          {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Ambil Data'}
        </Button>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">Nomor Aju</TableHead>
                <TableHead className="font-semibold text-white">Kode Dokumen</TableHead>
                <TableHead className="font-semibold text-white">Nomor Daftar</TableHead>
                <TableHead className="font-semibold text-white">Tanggal Daftar</TableHead>
                <TableHead className="font-semibold text-white">Kode Kantor</TableHead>
                <TableHead className="font-semibold text-white">Nama Kantor</TableHead>
                <TableHead className="font-semibold text-white">Kode TPS</TableHead>
                <TableHead className="font-semibold text-white">Nama Gudang</TableHead>
                <TableHead className="font-semibold text-white">ID Pengusha</TableHead>
                <TableHead className="font-semibold text-white">Nama Pengusaha</TableHead>
                <TableHead className="font-semibold text-white">Uraian</TableHead>
                <TableHead className="font-semibold text-white">Nomor Kontainer</TableHead>
                <TableHead className="font-semibold text-white">Nomor Segel</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={13} className="text-center py-8">
                    <Loader2 className="mx-auto h-6 w-6 animate-spin" />
                  </TableCell>
                </TableRow>
              ) : error ? (
                <TableRow>
                  <TableCell colSpan={13} className="text-center py-8 text-red-500">
                    {error}
                  </TableCell>
                </TableRow>
              ) : dokumen ? (
                <TableRow className="hover:bg-gray-50">
                  <TableCell>{dokumen.nomorAju}</TableCell>
                  <TableCell>{dokumen.kodeDokumen}</TableCell>
                  <TableCell>{dokumen.nomorDaftar}</TableCell>
                  <TableCell>{dokumen.tanggalDaftar}</TableCell>
                  <TableCell>{dokumen.kodeKantor}</TableCell>
                  <TableCell>{dokumen.namaKantor}</TableCell>
                  <TableCell>{dokumen.kodeTps}</TableCell>
                  <TableCell>{dokumen.namaGudang}</TableCell>
                  <TableCell>{dokumen.idPengusha}</TableCell>
                  <TableCell>{dokumen.namaPengusaha}</TableCell>
                  <TableCell>{dokumen.uraian}</TableCell>
                  <TableCell>{dokumen.nomorKontainer}</TableCell>
                  <TableCell>{dokumen.nomorSegel}</TableCell>
                </TableRow>
              ) : (
                <TableRow>
                  <TableCell colSpan={13} className="text-center py-8 text-gray-500">
                    Data akan muncul di sini setelah pencarian.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default DokumenKepabean;
