import { useState, useEffect } from 'react';
import { ArrowLeft, Shield, Smartphone, Building, Tag } from 'lucide-react';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';

interface DetailESealProps {
  esealId: string;
  onBack: () => void;
}

interface ESealMasterData {
  id: string;
  noEseal: string;
  noImei: string;
  vendor: string;
  tipe: string;
  status: 'AKTIF' | 'TIDAK_AKTIF' | 'INACTIVE';
}

// Dummy master data untuk E-Seal
const dummyESealMasterData: ESealMasterData = {
  id: '1',
  noEseal: 'E-Seal 11198271 - 1',
  noImei: '11198271',
  vendor: '373c041c-f9b8-491f-a4f6-7cc47a0569d3',
  tipe: 'BOLT_SEAL',
  status: 'AKTIF'
};

export default function DetailESeal({ esealId, onBack }: DetailESealProps) {
  const [esealDetail, setESealDetail] = useState<ESealMasterData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    const fetchESealDetail = async () => {
      setLoading(true);
      // Simulate loading delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setESealDetail(dummyESealMasterData);
      setLoading(false);
    };

    fetchESealDetail();
  }, [esealId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!esealDetail) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-500 mb-4">E-Seal tidak ditemukan</p>
          <Button onClick={onBack} variant="outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AKTIF':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'TIDAK_AKTIF':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'INACTIVE':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="flex items-center hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali
              </Button>
              <div className="min-w-0 flex-1">
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900 truncate">
                  Detail E-Seal - Master Data
                </h1>
                <p className="text-sm text-gray-500 truncate">
                  Informasi master data E-Seal {esealDetail.noEseal}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3 flex-shrink-0">
              <Badge
                className={`px-3 py-1 text-sm font-medium ${getStatusColor(esealDetail.status)}`}
              >
                {esealDetail.status}
              </Badge>
              <Button size="sm" variant="outline" className="hidden sm:flex">
                Admin
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Master Data E-Seal */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <Shield className="w-5 h-5 mr-2 text-blue-600" />
              Master Data E-Seal
            </h2>
            <p className="text-sm text-gray-600 mt-1">Informasi dasar dan identitas E-Seal</p>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Nomor E-Seal */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide flex items-center">
                  <Shield className="w-3 h-3 mr-1" />
                  Nomor E-Seal
                </label>
                <div className="bg-blue-50 px-4 py-3 rounded-lg border border-blue-200">
                  <p className="text-lg font-semibold text-blue-900 break-all">
                    {esealDetail.noEseal}
                  </p>
                </div>
              </div>

              {/* Nomor IMEI */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide flex items-center">
                  <Smartphone className="w-3 h-3 mr-1" />
                  Nomor IMEI
                </label>
                <div className="bg-gray-50 px-4 py-3 rounded-lg border border-gray-200">
                  <p className="text-lg font-mono text-gray-900 break-all">
                    {esealDetail.noImei}
                  </p>
                </div>
              </div>

              {/* Vendor */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide flex items-center">
                  <Building className="w-3 h-3 mr-1" />
                  Vendor
                </label>
                <div className="bg-purple-50 px-4 py-3 rounded-lg border border-purple-200">
                  <p className="text-sm font-mono text-purple-800 break-all">
                    {esealDetail.vendor}
                  </p>
                </div>
              </div>

              {/* Tipe */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide flex items-center">
                  <Tag className="w-3 h-3 mr-1" />
                  Tipe
                </label>
                <div className="bg-orange-50 px-4 py-3 rounded-lg border border-orange-200">
                  <p className="text-sm font-semibold text-orange-800">
                    {esealDetail.tipe}
                  </p>
                </div>
              </div>

              {/* Status */}
              <div className="md:col-span-2 space-y-2">
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Status (Aktif / Tidak Aktif / INACTIVE)
                </label>
                <div className="flex items-center">
                  <Badge
                    className={`px-4 py-2 text-sm font-medium ${getStatusColor(esealDetail.status)}`}
                  >
                    {esealDetail.status}
                  </Badge>
                  <span className="ml-3 text-xs text-gray-500">
                    → Status fisik/operasional si e-seal
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
