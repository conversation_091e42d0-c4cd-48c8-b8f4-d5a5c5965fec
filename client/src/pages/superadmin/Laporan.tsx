import React, { useState } from 'react';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import { Download, Save, Truck, Clock, FileText, BarChart3, Settings } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';


// Dummy data untuk laporan sesuai spesifikasi
const dummyLaporanData = [
  {
    id: '1',
    no: 1,
    noESeal: '7848585252',
    noAju: 'AJU123457684663',
    noIMEI: '875298572967967922',
    event: 'UNLOCKED',
    count: 1,
    duration: '01 h 44 m 25 s',
    mileage: '28',
    maxSpeed: '41',
    avgSpeed: '17.73668514',
    startDateTime: '2025-07-07 17:02:18',
    endDateTime: '2025-07-07 17:07:18',
    startMileage: '198',
    endMileage: '200',
    tripMileage: '2',
    tripDuration: '01 h 44 m 25 s',
    driverName: 'Budi <PERSON>a',
    startAddress: 'Kendal, Jawa Tengah',
    endAddress: 'Surabaya, Jawa Timur',
    startLat: '-6.9175',
    startLng: '110.1625',
    endLat: '-7.2575',
    endLng: '112.7521'
  },
  {
    id: '2',
    no: 2,
    noESeal: '7612340022',
    noAju: 'AJU123457684663',
    noIMEI: '875298572967967922',
    event: 'LOCKED',
    count: 1,
    duration: '02 h 15 m 30 s',
    mileage: '35',
    maxSpeed: '55',
    avgSpeed: '22.5',
    startDateTime: '2025-07-08 09:15:30',
    endDateTime: '2025-07-08 11:31:00',
    startMileage: '250',
    endMileage: '285',
    tripMileage: '35',
    tripDuration: '02 h 15 m 30 s',
    driverName: 'Ahmad Wijaya',
    startAddress: 'Semarang, Jawa Tengah',
    endAddress: 'Yogyakarta, DIY',
    startLat: '-6.9667',
    startLng: '110.4167',
    endLat: '-7.7956',
    endLng: '110.3695'
  }
];



const Laporan: React.FC = () => {
  const [selectedEseal, setSelectedEseal] = useState('');
  const [date, setDate] = useState('');
  const [exportAddress, setExportAddress] = useState(false);

  const handleDownload = () => {
    if (!selectedEseal || !date) {
      alert('Silakan pilih E-Seal dan tanggal terlebih dahulu.');
      return;
    }
    // Implement download functionality
    console.log('Downloading report...');
  };

  const handleSave = () => {
    // Implement save functionality
    console.log('Saving report...');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Laporan</h1>
        <div className="h-1 w-20 bg-slate-800 rounded"></div>
      </div>

      {/* Filter Section */}
      <div className="bg-white p-4 rounded-lg border">
        <div className="flex flex-col sm:flex-row gap-4 items-end">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Truck className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">PILIH NOMOR E-SEAL</span>
            </div>
            <Select value={selectedEseal} onValueChange={setSelectedEseal}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih E-Seal" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7848585252">7848585252</SelectItem>
                <SelectItem value="7612340022">7612340022</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">PERIODE</span>
            </div>
            <Input
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button onClick={handleDownload} className="bg-slate-800 hover:bg-slate-700 text-white">
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button onClick={handleSave} className="bg-slate-800 hover:bg-slate-700 text-white">
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="exportAddress"
              checked={exportAddress}
              onChange={(e) => setExportAddress(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="exportAddress" className="text-sm text-gray-700">
              Export with Address
            </label>
          </div>
        </div>
      </div>

      {/* Action Icons */}
      <div className="flex gap-2">
        <Button variant="outline" size="sm" className="p-2">
          <FileText className="w-4 h-4" />
        </Button>
        <Button variant="outline" size="sm" className="p-2">
          <BarChart3 className="w-4 h-4" />
        </Button>
        <div className="ml-auto flex gap-2">
          <Button variant="outline" size="sm" className="p-2">
            <Settings className="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" className="p-2">
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>



      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">No</TableHead>
                <TableHead className="font-semibold text-white">No E-Seal</TableHead>
                <TableHead className="font-semibold text-white">No Aju</TableHead>
                <TableHead className="font-semibold text-white">No IMEI</TableHead>
                <TableHead className="font-semibold text-white">Event</TableHead>
                <TableHead className="font-semibold text-white">Count</TableHead>
                <TableHead className="font-semibold text-white">Duration</TableHead>
                <TableHead className="font-semibold text-white">Mileage</TableHead>
                <TableHead className="font-semibold text-white">Max Speed(Km/h)</TableHead>
                <TableHead className="font-semibold text-white">Avg Speed(Km/h)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {dummyLaporanData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8 text-gray-500">
                    Data Tidak Ditemukan
                  </TableCell>
                </TableRow>
              ) : (
                dummyLaporanData.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{item.no}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noESeal}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noAju}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noIMEI}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        item.event === 'UNLOCKED' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {item.event}
                      </span>
                    </TableCell>
                    <TableCell className="text-center">{item.count}</TableCell>
                    <TableCell className="font-mono text-sm">{item.duration}</TableCell>
                    <TableCell className="text-center">{item.mileage}</TableCell>
                    <TableCell className="text-center">{item.maxSpeed}</TableCell>
                    <TableCell className="text-center">{item.avgSpeed}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>


    </div>
  );
};

export default Laporan;
