import React, { useState, useEffect } from 'react';
import { Input } from '../../components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import { Search } from 'lucide-react';
import MapTiler from '../../components/map/MapTiler';

// Dummy data untuk monitoring sesuai spesifikasi
const dummyMonitoringData = [
  {
    id: '1',
    no: 1,
    icon: '📍',
    noESeal: '7848585252',
    noIMEI: '875298572967967922',
    address: 'Jalan Pantura, Kendal, Jawa Tengah',
    posisiAltitude: '636373737',
    dayaBaterai: '15000',
    dayaAki: '350000',
    event: 'UNLOCKED',
    idVendor: '884CKEL637',
    kecepatanKontainer: '500',
    posisiLatitude: '-6.9175',
    posisiLongitude: '110.1625',
    provinsi: '<PERSON><PERSON> Tengah',
    kota: 'Ken<PERSON>',
    token: '123567888',
    status: 'ONLINE'
  },
  {
    id: '2',
    no: 2,
    icon: '📍',
    noESeal: '6968575494',
    noIMEI: '123456789012345',
    address: 'Jalan Raya Semarang, Semarang, Jawa Tengah',
    posisiAltitude: '525252525',
    dayaBaterai: '18000',
    dayaAki: '380000',
    event: 'LOCKED',
    idVendor: '884CKEL638',
    kecepatanKontainer: '450',
    posisiLatitude: '-6.9667',
    posisiLongitude: '110.4167',
    provinsi: 'Jawa Tengah',
    kota: 'Semarang',
    token: '123567889',
    status: 'ONLINE'
  }
];

const Monitoring: React.FC = () => {
  const [search, setSearch] = useState('');
  const [selectedEseals, setSelectedEseals] = useState<string[]>([]);

  const filteredData = dummyMonitoringData.filter(item =>
    item.noESeal.toLowerCase().includes(search.toLowerCase()) ||
    item.address.toLowerCase().includes(search.toLowerCase())
  );

  const handleSelectEseal = (esealId: string) => {
    setSelectedEseals(prev =>
      prev.includes(esealId)
        ? prev.filter(id => id !== esealId)
        : [...prev, esealId]
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Monitoring E-Seal</h1>
          <p className="text-gray-600">Real-time monitoring lokasi dan status E-Seal</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Panel - Controls and E-Seal List */}
        <div className="space-y-4">
          {/* Search */}
          <div className="bg-white p-4 rounded-lg border">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Cari E-Seal atau alamat..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* E-Seal Selection */}
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Pilih E-Seal</h3>
              <span className="text-xs text-gray-500">
                {selectedEseals.length} dipilih
              </span>
            </div>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {filteredData.map((eseal) => (
                <div
                  key={eseal.id}
                  className={`flex items-center justify-between p-2 rounded cursor-pointer transition-colors ${
                    selectedEseals.includes(eseal.id)
                      ? 'bg-blue-50 border border-blue-200'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleSelectEseal(eseal.id)}
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{eseal.icon}</span>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{eseal.noESeal}</p>
                      <p className="text-xs text-gray-500">{eseal.kota}</p>
                    </div>
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    eseal.status === 'ONLINE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {eseal.status}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Panel - Map */}
        <div className="lg:col-span-2 bg-white p-4 rounded-lg border">
          <div className="h-96">
            <MapTiler
              originLocation={{
                lat: parseFloat(filteredData[0]?.posisiLatitude || '-6.9175'),
                lng: parseFloat(filteredData[0]?.posisiLongitude || '110.1625'),
                address: filteredData[0]?.address || 'Kendal, Jawa Tengah'
              }}
              interactive={true}
              height="100%"
            />
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">No</TableHead>
                <TableHead className="font-semibold text-white">Icon</TableHead>
                <TableHead className="font-semibold text-white">No E-Seal</TableHead>
                <TableHead className="font-semibold text-white">No IMEI</TableHead>
                <TableHead className="font-semibold text-white">Address</TableHead>
                <TableHead className="font-semibold text-white">Event</TableHead>
                <TableHead className="font-semibold text-white">Status</TableHead>
                <TableHead className="font-semibold text-white">Latitude</TableHead>
                <TableHead className="font-semibold text-white">Longitude</TableHead>
                <TableHead className="font-semibold text-white">Provinsi</TableHead>
                <TableHead className="font-semibold text-white">Kota</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.map((item) => (
                <TableRow key={item.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">{item.no}</TableCell>
                  <TableCell className="text-center text-lg">{item.icon}</TableCell>
                  <TableCell className="font-mono text-sm">{item.noESeal}</TableCell>
                  <TableCell className="font-mono text-sm">{item.noIMEI}</TableCell>
                  <TableCell className="text-sm max-w-xs truncate" title={item.address}>
                    {item.address}
                  </TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      item.event === 'UNLOCKED' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                    }`}>
                      {item.event}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      item.status === 'ONLINE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {item.status}
                    </span>
                  </TableCell>
                  <TableCell className="font-mono text-sm">{item.posisiLatitude}</TableCell>
                  <TableCell className="font-mono text-sm">{item.posisiLongitude}</TableCell>
                  <TableCell className="text-sm">{item.provinsi}</TableCell>
                  <TableCell className="text-sm">{item.kota}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default Monitoring;