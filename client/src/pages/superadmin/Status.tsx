import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';

export default function Status() {
  const [nomorAju, setNomorAju] = useState('');
  const [nomorESeal, setNomorESeal] = useState('');
  const [token, setToken] = useState('');
  const [showResult, setShowResult] = useState(false);

  // Dummy data untuk hasil status
  const statusData = {
    nomorESeal: nomorESeal || '7848585252',
    status: 'Data Masih Kosong',
    actions: [
      { action: 'Start', updatePosition: '', stop: '' },
    ]
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (nomorAju && nomorESeal && token) {
      setShowResult(true);
    } else {
      alert('Semua kolom wajib diisi!');
    }
  };

  const handleReset = () => {
    setNomorAju('');
    setNomorESeal('');
    setToken('');
    setShowResult(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Tracking Data - Status</h1>
        <p className="text-gray-600">Cek status tracking E-Seal dengan memasukkan informasi yang diperlukan.</p>
      </div>

      {/* Form Input */}
      <div className="bg-white rounded-lg border p-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Nomor Aju */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nomor Aju <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Masukkan Nomor Aju"
                value={nomorAju}
                onChange={(e) => setNomorAju(e.target.value)}
                required
              />
            </div>

            {/* Nomor E-Seal */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nomor E-Seal <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Masukkan Nomor E-Seal"
                value={nomorESeal}
                onChange={(e) => setNomorESeal(e.target.value)}
                required
              />
            </div>
          </div>

          {/* Token */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Token <span className="text-red-500">*</span>
            </label>
            <Input
              placeholder="Masukkan Token"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              required
            />
          </div>

          <p className="text-sm text-red-500">* Kolom wajib diisi</p>

          {/* Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleReset}
              className="flex-1"
            >
              Reset
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-slate-800 hover:bg-slate-700 text-white"
            >
              Ambil Data
            </Button>
          </div>
        </form>
      </div>

      {/* Result Section */}
      {showResult && (
        <div className="space-y-4">
          {/* Status Info */}
          <div className="bg-white rounded-lg border p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Nomor E-Seal: {statusData.nomorESeal}
            </h2>
            
            {/* Status Table */}
            <div className="bg-white rounded-lg border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow className="bg-slate-800">
                    <TableHead className="font-semibold text-white text-center">Start</TableHead>
                    <TableHead className="font-semibold text-white text-center">Update Position</TableHead>
                    <TableHead className="font-semibold text-white text-center">Stop</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {statusData.actions.map((action, index) => (
                    <TableRow key={index}>
                      <TableCell className="text-center py-4">{action.start || '-'}</TableCell>
                      <TableCell className="text-center py-4">{action.updatePosition || '-'}</TableCell>
                      <TableCell className="text-center py-4">{action.stop || '-'}</TableCell>
                    </TableRow>
                  ))}
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-8 text-gray-500">
                      {statusData.status}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      )}

      {/* Info Message when no data */}
      {!showResult && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Informasi
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  Silakan masukkan Nomor Aju, Nomor E-Seal, dan Token untuk melihat status tracking E-Seal.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
