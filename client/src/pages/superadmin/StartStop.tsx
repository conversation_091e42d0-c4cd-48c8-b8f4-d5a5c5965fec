import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { Search, Eye, Play, Square, MapPin } from 'lucide-react';

// Dummy data untuk tracking start-stop sesuai spesifikasi
const dummyStartStopData = [
  {
    id: '1',
    no: 1,
    event: 'Unlocked',
    statusGerak: 'MOVING',
    alamatAsal: 'Surabaya',
    alamatTujuan: 'Banten',
    idVendor: '884CKEL637',
    jenisKontainer: 'GENERAL / DRY CARGO',
    latitudeAsal: '-6.9175',
    longitudeAsal: '110.1625',
    lokasiAsal: 'Kendal, Jawa Tengah',
    latitudeTujuan: '-6.1751',
    longitudeTujuan: '106.8650',
    lokasiTujuan: 'Jakarta Utara, DKI Jakarta',
    jarakTempuh: '105 Km',
    nomorIMEI: '875298572967967922',
    nomorESeal: '7848585252',
    nomorKontainer: '473',
    nomorPolisi: 'AE 547 GA',
    token: '2672727262',
    ukuranKontainer: '40 FEET',
    namaDriver: 'Budi Putra',
    nomorTeleponDriver: '081262426626',
    jenisMuat: 'Muat Eksport',
    jumlahKontainer: 1,
    kodeDokumen: '123',
    kodeKantor: '123',
    nomorAju: '457',
    nomorDokumen: '25 Jan 2025',
    tanggalDokumen: '02 Mar 2025'
  },
  {
    id: '2',
    no: 2,
    event: 'Unlocked',
    statusGerak: 'STOP',
    alamatAsal: 'Semarang',
    alamatTujuan: 'Sleman',
    idVendor: '262ABCD203',
    jenisKontainer: 'GENERAL / DRY CARGO',
    latitudeAsal: '-11.4.14554',
    longitudeAsal: '41.41451.11',
    lokasiAsal: 'Semarang, Jawa Tengah',
    latitudeTujuan: '-42.514.1414',
    longitudeTujuan: '41.41451.11',
    lokasiTujuan: 'Sleman, DIY',
    jarakTempuh: '60 Km',
    nomorIMEI: '383652264754968868',
    nomorESeal: '6968575494',
    nomorKontainer: '440',
    nomorPolisi: 'B 1414 CC',
    token: '5437537524',
    ukuranKontainer: '40 FEET',
    namaDriver: 'Budi Putra',
    nomorTeleponDriver: '081363836215',
    jenisMuat: 'Muat Eksport',
    jumlahKontainer: 1,
    kodeDokumen: '656',
    kodeKantor: '789',
    nomorAju: '1416',
    nomorDokumen: '02 Mar 2025',
    tanggalDokumen: '02 Mar 2025'
  }
];

export default function StartStop() {
  const [search, setSearch] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState('10');
  const [currentPage, setCurrentPage] = useState(1);

  // Filter data berdasarkan search
  const filteredData = dummyStartStopData.filter(item =>
    item.nomorESeal.toLowerCase().includes(search.toLowerCase()) ||
    item.nomorIMEI.toLowerCase().includes(search.toLowerCase()) ||
    item.alamatAsal.toLowerCase().includes(search.toLowerCase()) ||
    item.alamatTujuan.toLowerCase().includes(search.toLowerCase())
  );

  const totalPages = Math.ceil(filteredData.length / parseInt(entriesPerPage));
  const startIndex = (currentPage - 1) * parseInt(entriesPerPage);
  const currentData = filteredData.slice(startIndex, startIndex + parseInt(entriesPerPage));

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'MOVING':
        return <Badge className="bg-green-100 text-green-800">MOVING</Badge>;
      case 'STOP':
        return <Badge className="bg-red-100 text-red-800">STOP</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const handleDetail = (id: string) => {
    console.log('View detail for tracking:', id);
    // TODO: Implement detail view
  };

  const handleStart = (id: string) => {
    console.log('Start tracking for:', id);
    // TODO: Implement start tracking
  };

  const handleStop = (id: string) => {
    console.log('Stop tracking for:', id);
    // TODO: Implement stop tracking
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Tracking Data - Start Stop</h1>
        <p className="text-gray-600">Kelola tracking start dan stop untuk monitoring E-Seal.</p>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white p-4 rounded-lg border">
        {/* Show entries */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Show</span>
          <Select value={entriesPerPage} onValueChange={setEntriesPerPage}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-gray-600">entries per page</span>
        </div>

        {/* Search */}
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Cari tracking data..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 w-48 sm:w-64"
            />
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">No</TableHead>
                <TableHead className="font-semibold text-white">Event</TableHead>
                <TableHead className="font-semibold text-white">Status Gerak</TableHead>
                <TableHead className="font-semibold text-white">Alamat Asal</TableHead>
                <TableHead className="font-semibold text-white">Alamat Tujuan</TableHead>
                <TableHead className="font-semibold text-white">ID Vendor</TableHead>
                <TableHead className="font-semibold text-white">Jenis Kontainer</TableHead>
                <TableHead className="font-semibold text-white">Latitude Asal</TableHead>
                <TableHead className="font-semibold text-white">Longitude Asal</TableHead>
                <TableHead className="font-semibold text-white">Lokasi Asal</TableHead>
                <TableHead className="font-semibold text-white">Latitude Tujuan</TableHead>
                <TableHead className="font-semibold text-white">Longitude Tujuan</TableHead>
                <TableHead className="font-semibold text-white">Lokasi Tujuan</TableHead>
                <TableHead className="font-semibold text-white">Jarak Tempuh</TableHead>
                <TableHead className="font-semibold text-white">Nomor IMEI</TableHead>
                <TableHead className="font-semibold text-white">Nomor E-Seal</TableHead>
                <TableHead className="font-semibold text-white">Nomor Kontainer</TableHead>
                <TableHead className="font-semibold text-white">Nomor Polisi</TableHead>
                <TableHead className="font-semibold text-white">Token</TableHead>
                <TableHead className="font-semibold text-white">Ukuran Kontainer</TableHead>
                <TableHead className="font-semibold text-white">Nama Driver</TableHead>
                <TableHead className="font-semibold text-white">Nomor Telepon Driver</TableHead>
                <TableHead className="font-semibold text-white">Jenis Muat</TableHead>
                <TableHead className="font-semibold text-white">Jumlah Kontainer</TableHead>
                <TableHead className="font-semibold text-white">Kode Dokumen</TableHead>
                <TableHead className="font-semibold text-white">Kode Kantor</TableHead>
                <TableHead className="font-semibold text-white">Nomor Aju</TableHead>
                <TableHead className="font-semibold text-white">Nomor Dokumen</TableHead>
                <TableHead className="font-semibold text-white">Tanggal Dokumen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={29} className="text-center py-8 text-gray-500">
                    {search ? 'Data tidak ditemukan' : 'Data masih kosong'}
                  </TableCell>
                </TableRow>
              ) : (
                currentData.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{item.no}</TableCell>
                    <TableCell className="text-sm">{item.event}</TableCell>
                    <TableCell>{getStatusBadge(item.statusGerak)}</TableCell>
                    <TableCell className="text-sm">{item.alamatAsal}</TableCell>
                    <TableCell className="text-sm">{item.alamatTujuan}</TableCell>
                    <TableCell className="font-mono text-sm">{item.idVendor}</TableCell>
                    <TableCell className="text-sm">{item.jenisKontainer}</TableCell>
                    <TableCell className="font-mono text-sm">{item.latitudeAsal}</TableCell>
                    <TableCell className="font-mono text-sm">{item.longitudeAsal}</TableCell>
                    <TableCell className="text-sm">{item.lokasiAsal}</TableCell>
                    <TableCell className="font-mono text-sm">{item.latitudeTujuan}</TableCell>
                    <TableCell className="font-mono text-sm">{item.longitudeTujuan}</TableCell>
                    <TableCell className="text-sm">{item.lokasiTujuan}</TableCell>
                    <TableCell className="text-sm">{item.jarakTempuh}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorIMEI}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorESeal}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorKontainer}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorPolisi}</TableCell>
                    <TableCell className="font-mono text-sm">{item.token}</TableCell>
                    <TableCell className="text-sm">{item.ukuranKontainer}</TableCell>
                    <TableCell className="text-sm">{item.namaDriver}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorTeleponDriver}</TableCell>
                    <TableCell className="text-sm">{item.jenisMuat}</TableCell>
                    <TableCell className="text-sm">{item.jumlahKontainer}</TableCell>
                    <TableCell className="font-mono text-sm">{item.kodeDokumen}</TableCell>
                    <TableCell className="font-mono text-sm">{item.kodeKantor}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorAju}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorDokumen}</TableCell>
                    <TableCell className="text-sm">{item.tanggalDokumen}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>



      {/* Pagination info */}
      {filteredData.length > 0 && (
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>
            Showing {startIndex + 1} to {Math.min(startIndex + parseInt(entriesPerPage), filteredData.length)} of {filteredData.length} entries
          </span>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="px-3 py-1 bg-slate-800 text-white rounded text-xs">
              {currentPage}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
