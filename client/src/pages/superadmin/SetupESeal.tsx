import { useState } from 'react';
import { Button } from '../../components/ui/button';
import { ChevronLeft, ChevronRight, MapPin, Truck, FileCheck, RotateCcw } from 'lucide-react';
import type { SetupESealFormData } from '@shared';

// Import komponen-komponen yang sudah dipecah
import { StepProgress } from '../../components/setup-eseal/StepProgress';
import { Step1DataESeal } from '../../components/setup-eseal/Step1DataESeal';
import { Step2Lokasi } from '../../components/setup-eseal/Step2Lokasi';
import { Step3KendaraanDriver } from '../../components/setup-eseal/Step3KendaraanDriver';
import { Step4ValidasiAJU } from '../../components/setup-eseal/Step4ValidasiAJU';
import { Step5Review } from '../../components/setup-eseal/Step5Review';
import { StopModal } from '../../components/setup-eseal/StopModal';

interface SetupESealProps {
  esealData?: {
    id: string;
    nama: string;
    nomorIMEI: string;
    merk?: string;
    model?: string;
    tipe?: string;
    idVendor?: string;
  };
  onBack: () => void;
}

export default function SetupESeal({ esealData, onBack }: SetupESealProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [showStopModal, setShowStopModal] = useState(false);

  // Updated form data structure sesuai dengan API Beacukai
  const [formData, setFormData] = useState<SetupESealFormData>({
    // Step 1 - Data E-Seal (mapped to API fields)
    organizationId: '',
    alamatAsal: '',              // alamatESeal → alamatAsal
    alamatTujuan: '',            // alamatTujuan → alamatTujuan
    idVendor: esealData?.idVendor || '',     // idVendor → idVendor
    noEseal: esealData?.nama || 'Control: ESL-2025-001',  // nomorESeal → noEseal
    noImei: esealData?.nomorIMEI || '863420978654322',    // nomorIMEI → noImei
    merk: esealData?.merk || '',         // merk E-Seal (dari data yang sudah ada)
    model: esealData?.model || '',       // model E-Seal (dari data yang sudah ada)
    tipe: esealData?.tipe || '',         // tipe E-Seal (dari data yang sudah ada)
    token: '',                   // token → token (akan diisi dari JWT)

    // Step 2 - Lokasi (new fields for API)
    lokasiAsal: '',              // lokasiAsal → lokasiAsal
    lokasiTujuan: '',            // lokasiTujuan → lokasiTujuan
    latitudeAsal: '',            // NEW - required by API
    longitudeAsal: '',           // NEW - required by API
    latitudeTujuan: '',          // NEW - required by API
    longitudeTujuan: '',         // NEW - required by API

    // Step 3 - Kendaraan & Driver (mapped to API fields)
    noPolisi: '',                // nomorPolisi → noPolisi
    ukKontainer: '',             // ukuranKontainer → ukKontainer
    jnsKontainer: '',            // jenisKontainer → jnsKontainer
    noKontainer: '',             // nomorKontainer → noKontainer
    namaDriver: '',              // namaDriver → namaDriver
    nomorTeleponDriver: '',      // noTeleponDriver → nomorTeleponDriver

    // Step 4 - Validasi AJU & Dokumen
    nomorAJU: '',
    statusValidasi: 'pending',   // pending, validating, valid, invalid
    dokumen: [{                  // NEW - required by API
      jenisMuat: '',
      jumlahKontainer: '',
      kodeDokumen: '',
      kodeKantor: '',
      nomorAju: '',
      nomorDokumen: '',
      tanggalDokumen: ''
    }]
  });

  const steps = [
    { id: 1, title: 'Data E-Seal', icon: FileCheck, active: true },
    { id: 2, title: 'Lokasi', icon: MapPin, active: false },
    { id: 3, title: 'Kendaraan & Driver', icon: Truck, active: false },
    { id: 4, title: 'Validasi AJU', icon: FileCheck, active: false },
    { id: 5, title: 'Review', icon: RotateCcw, active: false }
  ];

  // Validation functions untuk setiap step
  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        // Step 1: Validasi data E-Seal dasar
        return !!(formData.idVendor && formData.noImei && formData.noEseal &&
                 formData.merk && formData.model && formData.tipe);
      case 2:
        // Step 2: Validasi lokasi dan koordinat (termasuk alamat)
        return !!(formData.lokasiAsal && formData.lokasiTujuan &&
                 formData.latitudeAsal && formData.longitudeAsal &&
                 formData.latitudeTujuan && formData.longitudeTujuan &&
                 formData.alamatAsal && formData.alamatTujuan);
      case 3:
        return !!(formData.noPolisi && formData.ukKontainer && formData.jnsKontainer &&
                 formData.noKontainer && formData.namaDriver && formData.nomorTeleponDriver);
      case 4:
        return formData.statusValidasi === 'valid' && formData.dokumen.length > 0;
      default:
        return true;
    }
  };

  const handleNext = async () => {
    if (currentStep < 5) {
      if (validateStep(currentStep)) {
        setCurrentStep(currentStep + 1);
      } else {
        // Show validation error
        alert('Mohon lengkapi semua field yang diperlukan');
      }
    } else {
      // Submit form: Update existing E-Seal and start tracking
      try {
        if (!esealData?.id) {
          throw new Error("E-Seal ID is missing. Cannot update.");
        }

        // 1. Update E-Seal data in the database and set status to ACTIVE
        const updatePayload = {
          ...formData,
          status: 'ACTIVE' // Set status to ACTIVE
        };

        const updateResponse = await fetch(`/api/eseal/${esealData.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updatePayload),
        });

        if (!updateResponse.ok) {
          const errorResult = await updateResponse.json();
          throw new Error(errorResult.error || 'Failed to update E-Seal data');
        }
        console.log('✅ E-Seal updated successfully, now starting tracking...');

        // 2. Start tracking via Beacukai API
        const trackingData = {
          alamatAsal: formData.alamatAsal,
          alamatTujuan: formData.alamatTujuan,
          idVendor: formData.idVendor,
          jnsKontainer: formData.jnsKontainer,
          latitudeAsal: formData.latitudeAsal,
          latitudeTujuan: formData.latitudeTujuan,
          lokasiAsal: formData.lokasiAsal,
          lokasiTujuan: formData.lokasiTujuan,
          longitudeAsal: formData.longitudeAsal,
          longitudeTujuan: formData.longitudeTujuan,
          noImei: formData.noImei,
          noEseal: formData.noEseal,
          noKontainer: formData.noKontainer,
          noPolisi: formData.noPolisi,
          ukKontainer: formData.ukKontainer,
          namaDriver: formData.namaDriver,
          nomorTeleponDriver: formData.nomorTeleponDriver,
          dokumen: formData.dokumen,
          token: formData.token,
        };

        console.log('🚀 Starting tracking with data:', trackingData);

        const trackingResponse = await fetch('/api/beacukai/tracking/start', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(trackingData),
        });

        if (!trackingResponse.ok) {
          const errorText = await trackingResponse.text();
          console.error('Tracking API failed:', trackingResponse.status, errorText);
          alert('E-Seal berhasil diaktifkan, namun gagal memulai tracking di sistem Beacukai. Silakan coba lagi dari halaman detail.');
          onBack();
          return;
        }

        const trackingResult = await trackingResponse.json();
        console.log('📊 Tracking result:', trackingResult);

        if (trackingResult.success) {
          alert('🎉 E-Seal berhasil diaktifkan dan tracking dimulai!');
        } else {
          alert(`E-Seal berhasil diaktifkan, namun gagal memulai tracking: ${trackingResult.message || 'Unknown error'}`);
        }

        onBack(); // Return to main page
      } catch (error) {
        console.error('❌ Error submitting form:', error);
        alert(`Terjadi kesalahan: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      setShowStopModal(true);
    }
  };

  const confirmStopMonitoring = () => {
    setShowStopModal(false);
    if (onBack) {
      onBack();
    }
  };

  const cancelStopMonitoring = () => {
    setShowStopModal(false);
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <Step1DataESeal formData={formData} setFormData={setFormData} />;
      case 2:
        return <Step2Lokasi formData={formData} setFormData={setFormData} />;
      case 3:
        return <Step3KendaraanDriver formData={formData} setFormData={setFormData} />;
      case 4:
        return <Step4ValidasiAJU formData={formData} setFormData={setFormData} />;
      case 5:
        return <Step5Review formData={formData} />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Setup E-Seal Monitoring</h1>
        <p className="text-gray-600">Ikuti langkah-langkah berikut untuk mengatur monitoring E-Seal dengan integrasi API Beacukai.</p>
      </div>

      {/* Progress Steps */}
      <StepProgress currentStep={currentStep} steps={steps} />

      {/* Form Content */}
      <div className="bg-white rounded-lg border p-6">
        {renderStepContent()}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleBack}
          className="flex items-center"
        >
          <ChevronLeft className="w-4 h-4 mr-2" />
          {currentStep === 1 ? 'Kembali ke Data E-Seal' : 'Sebelumnya'}
        </Button>

        <Button
          onClick={handleNext}
          className="bg-slate-800 hover:bg-slate-700 text-white flex items-center"
          disabled={!validateStep(currentStep)}
        >
          {currentStep === 5 ? 'Mulai Monitoring' : 'Selanjutnya'}
          {currentStep < 5 && <ChevronRight className="w-4 h-4 ml-2" />}
        </Button>
      </div>

      {/* Stop Monitoring Modal */}
      <StopModal
        isOpen={showStopModal}
        onConfirm={confirmStopMonitoring}
        onCancel={cancelStopMonitoring}
      />
    </div>
  );
}
