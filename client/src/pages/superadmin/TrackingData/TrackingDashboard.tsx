import { useState, useEffect } from 'react';
import {
  MapPin,
  Play,
  RefreshCw,
  Battery,
  Wifi,
  WifiOff,
  Al<PERSON><PERSON>riangle,
  CheckCircle
} from 'lucide-react';

interface DeviceStatus {
  id: string;
  noEseal: string;
  noImei: string;
  currentStatus: string;
  gpsOnline: boolean;
  lastKnownLat?: string;
  lastKnownLng?: string;
  batteryLevel?: string;
  lastGpsUpdate?: string;
  registeredWithBeacukai: boolean;
}

interface TrackingSession {
  id: string;
  sessionStatus: string;
  startedAt: string;
  totalUpdates: number;
  lastUpdateAt?: string;
  eseal: {
    noEseal: string;
    noImei: string;
    deviceStatus: DeviceStatus;
  };
}

export default function TrackingDashboard() {
  const [devices, setDevices] = useState<any[]>([]);
  const [activeSessions, setActiveSessions] = useState<TrackingSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch data on component mount
  useEffect(() => {
    fetchAllData();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchAllData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchAllData = async () => {
    try {
      setRefreshing(true);
      
      // Fetch all data in parallel
      const [devicesRes, sessionsRes] = await Promise.all([
        fetch('/api/tracking/devices'),
        fetch('/api/tracking/active')
      ]);

      if (devicesRes.ok) {
        const devicesData = await devicesRes.json();
        setDevices(devicesData.data || []);
      }

      if (sessionsRes.ok) {
        const sessionsData = await sessionsRes.json();
        setActiveSessions(sessionsData.data || []);
      }

    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const triggerManualUpdate = async () => {
    try {
      setRefreshing(true);
      const response = await fetch('/api/tracking/trigger-position-update', {
        method: 'POST'
      });
      
      if (response.ok) {
        console.log('Manual position update triggered');
        // Refresh data after a short delay
        setTimeout(fetchAllData, 2000);
      }
    } catch (error) {
      console.error('Error triggering manual update:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-500 text-white';
      case 'inactive': return 'bg-gray-500 text-white';
      case 'error': return 'bg-red-500 text-white';
      default: return 'bg-yellow-500 text-white';
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('id-ID');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading tracking data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Tracking Dashboard</h1>
          <p className="text-gray-600">Real-time E-Seal monitoring and GPS tracking</p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={fetchAllData}
            disabled={refreshing}
            className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={triggerManualUpdate}
            disabled={refreshing}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            <Play className="h-4 w-4 mr-2" />
            Manual Update
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex items-center">
            <MapPin className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Devices</p>
              <p className="text-2xl font-bold">{devices.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex items-center">
            <Play className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Tracking</p>
              <p className="text-2xl font-bold">{activeSessions.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex items-center">
            <Wifi className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">GPS Online</p>
              <p className="text-2xl font-bold">
                {devices.filter(d => d.deviceStatus?.gpsOnline).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex items-center">
            <CheckCircle className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Beacukai Registered</p>
              <p className="text-2xl font-bold">
                {devices.filter(d => d.deviceStatus?.registeredWithBeacukai).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Device List */}
      <div className="bg-white rounded-lg border shadow-sm">
        <div className="p-6 border-b">
          <h2 className="text-xl font-semibold">Registered E-Seal Devices</h2>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {devices.map((device) => (
              <div key={device.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{device.noEseal}</h3>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(device.deviceStatus?.currentStatus || 'inactive')}`}>
                        {device.deviceStatus?.currentStatus || 'INACTIVE'}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">IMEI: {device.noImei}</p>
                    <p className="text-sm text-gray-600">Vendor: {device.idVendor}</p>

                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center gap-1">
                        {device.deviceStatus?.gpsOnline ? (
                          <Wifi className="h-4 w-4 text-green-500" />
                        ) : (
                          <WifiOff className="h-4 w-4 text-red-500" />
                        )}
                        <span>GPS {device.deviceStatus?.gpsOnline ? 'Online' : 'Offline'}</span>
                      </div>

                      {device.deviceStatus?.batteryLevel && (
                        <div className="flex items-center gap-1">
                          <Battery className="h-4 w-4" />
                          <span>{device.deviceStatus.batteryLevel}%</span>
                        </div>
                      )}

                      <div className="flex items-center gap-1">
                        {device.deviceStatus?.registeredWithBeacukai ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        )}
                        <span>
                          {device.deviceStatus?.registeredWithBeacukai ? 'Registered' : 'Not Registered'}
                        </span>
                      </div>
                    </div>

                    {device.deviceStatus?.lastKnownLat && device.deviceStatus?.lastKnownLng && (
                      <p className="text-sm text-gray-600">
                        Last Position: {device.deviceStatus.lastKnownLat}, {device.deviceStatus.lastKnownLng}
                      </p>
                    )}
                  </div>

                  <div className="text-right text-sm text-gray-500">
                    {device.deviceStatus?.lastGpsUpdate && (
                      <p>Last Update: {formatDateTime(device.deviceStatus.lastGpsUpdate)}</p>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {devices.length === 0 && (
              <p className="text-center text-gray-500 py-8">No devices registered yet</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
