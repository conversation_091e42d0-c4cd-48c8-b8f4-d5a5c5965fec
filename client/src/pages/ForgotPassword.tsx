import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Mail } from 'lucide-react';
import '../styles/auth.css';

export default function ForgotPassword() {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate sending reset email
    setTimeout(() => {
      setIsLoading(false);
      setIsEmailSent(true);
    }, 2000);
  };

  const handleBackToLogin = () => {
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center p-4">
      {/* Outer Floating Container - White Background */}
      <div className="w-full max-w-5xl bg-white rounded-3xl shadow-2xl p-8 lg:p-12">
        <div className="flex flex-col lg:flex-row items-center justify-center min-h-[500px] gap-8 lg:gap-12">
          
          {/* Left Side - Navy Forgot Password Card */}
          <div className="w-full max-w-md">
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl shadow-2xl p-8">
              {/* Logo Section */}
              <div className="text-center mb-8">
                <div className="inline-block border-2 border-white rounded-lg p-3 mb-4">
                  <div className="text-white">
                    <div className="text-lg font-bold">
                      V<sup className="text-xs">th</sup> <span className="text-blue-400">VIT Plus</span>
                    </div>
                    <div className="text-xs mt-1 border-t border-white pt-1">
                      Secure, Reliable & Accurate
                    </div>
                  </div>
                </div>
              </div>

              {/* Title */}
              <div className="text-center mb-8">
                <h1 className="text-2xl font-bold text-white mb-4">
                  Lupa Kata Sandi
                </h1>
                <div className="w-16 h-0.5 bg-white mx-auto"></div>
              </div>

              {!isEmailSent ? (
                /* Form State */
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Email Input */}
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="pl-10 h-12 bg-transparent border-2 border-gray-400 text-white placeholder-gray-400 focus:border-white focus:ring-0 rounded-lg"
                      required
                    />
                  </div>

                  {/* Buttons */}
                  <div className="flex space-x-3">
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="flex-1 h-12 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      {isLoading ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>Mengirim...</span>
                        </div>
                      ) : (
                        'Kirim'
                      )}
                    </Button>

                    <Button
                      type="button"
                      onClick={handleBackToLogin}
                      className="flex-1 h-12 bg-white text-slate-800 hover:bg-gray-100 font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      Kembali ke Login
                    </Button>
                  </div>
                </form>
              ) : (
                /* Success State */
                <div className="space-y-6">
                  {/* Success Message */}
                  <div className="text-center text-white">
                    <p className="text-sm mb-6">
                      Link reset kata sandi telah dikirim ke email.
                    </p>
                  </div>

                  {/* Back to Login Button */}
                  <Button
                    type="button"
                    onClick={handleBackToLogin}
                    className="w-full h-12 bg-white text-slate-800 hover:bg-gray-100 font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    Login
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Right Side - Illustration */}
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center max-w-md">
              {/* Illustration Area */}
              <div className="relative mb-8">
                <div className="w-80 h-80 mx-auto relative flex items-center justify-center">
                  <img 
                    src="/in-sync/amico.png" 
                    alt="Forgot Password Illustration" 
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
