import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Eye, EyeOff, Mail, Lock } from 'lucide-react';
import { signIn } from '@/lib/auth-client';

export default function Login() {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await signIn.email({
        email: formData.email,
        password: formData.password,
      });

      if (result.error) {
        console.error('Login error:', result.error);
        alert('Login gagal: ' + result.error.message);
      } else {
        // Navigate based on role
        const userRole = result.data?.user?.role; // Assuming role is available here
        if (userRole === 'superadmin') {
          navigate('/superadmin/data-eseal');
        } else if (userRole === 'admin') {
          navigate('/admin/data-eseal');
        } else {
          // Default or fallback route for other roles
          navigate('/dashboard'); // Or some other default route
        }
        // Reload to trigger auth state update
        window.location.reload();
      }
    } catch (error) {
      console.error('Login error:', error);
      alert('Login gagal. Silakan coba lagi.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center p-4">
      {/* Outer Floating Container - White Background */}
      <div className="w-full max-w-5xl bg-white rounded-3xl shadow-2xl p-4 sm:p-8 lg:p-12">
        <div className="flex flex-col lg:flex-row items-center justify-center min-h-[500px] gap-4 sm:gap-8 lg:gap-12">

          {/* Login Card - Centered on mobile, left side on desktop */}
          <div className="w-full max-w-md">
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl shadow-2xl p-6 sm:p-8">
              {/* Logo Section */}
              <div className="text-center mb-6 sm:mb-8">
                <div className="inline-block border-2 border-white rounded-lg p-2 sm:p-3 mb-3 sm:mb-4">
                  <div className="text-white">
                    <div className="text-base sm:text-lg font-bold">
                      V<sup className="text-xs">th</sup> <span className="text-blue-400">VIT Plus</span>
                    </div>
                    <div className="text-xs mt-1 border-t border-white pt-1">
                      Secure, Reliable & Accurate
                    </div>
                  </div>
                </div>
              </div>

              {/* Welcome Message */}
              <div className="text-center mb-6 sm:mb-8">
                <h1 className="text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4">
                  Selamat Datang!
                </h1>
                <div className="w-16 h-0.5 bg-white mx-auto"></div>
              </div>

              {/* Login Form */}
              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
                {/* Email Input */}
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="Email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="pl-10 h-10 sm:h-12 bg-transparent border-2 border-gray-400 text-white placeholder-gray-400 focus:border-white focus:ring-0 rounded-lg"
                    required
                  />
                </div>

                {/* Password Input */}
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Kata sandi"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="pl-10 pr-10 h-10 sm:h-12 bg-transparent border-2 border-gray-400 text-white placeholder-gray-400 focus:border-white focus:ring-0 rounded-lg"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                  >
                    {showPassword ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button>
                </div>

                {/* Forgot Password Link */}
                <div className="text-left space-y-2">
                  <button
                    type="button"
                    onClick={() => navigate('/forgot-password')}
                    className="text-sm text-gray-300 hover:text-white transition-colors block"
                  >
                    Lupa kata sandi?
                  </button>
                  <button
                    type="button"
                    onClick={() => navigate('/register')}
                    className="text-sm text-gray-300 hover:text-white transition-colors block"
                  >
                    Belum punya akun? Daftar di sini
                  </button>
                </div>

                {/* Login Button */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-10 sm:h-12 bg-white text-slate-800 hover:bg-gray-100 font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-slate-800 border-t-transparent rounded-full animate-spin"></div>
                      <span>Login...</span>
                    </div>
                  ) : (
                    'Login'
                  )}
                </Button>
              </form>
            </div>
          </div>

          {/* Desktop Only - Illustration (Hidden on mobile) */}
          <div className="hidden lg:flex flex-1 items-center justify-center">
            <div className="text-center max-w-md">
              {/* Illustration Area */}
              <div className="relative mb-8">
                <div className="w-80 h-80 mx-auto relative flex items-center justify-center">
                  <img
                    src="/in-sync/amico.png"
                    alt="Login Illustration"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
