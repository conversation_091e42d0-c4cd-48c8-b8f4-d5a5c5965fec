/* Auth pages common styles */
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.auth-card-container {
  width: 100%;
  max-width: 80rem;
  background: white;
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 2rem;
}

@media (min-width: 1024px) {
  .auth-card-container {
    padding: 3rem;
  }
}

.auth-form-card {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 2rem;
}

.auth-logo {
  display: inline-block;
  border: 2px solid white;
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.auth-logo-text {
  color: white;
  font-size: 1.125rem;
  font-weight: bold;
}

.auth-logo-subtitle {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  border-top: 1px solid white;
  padding-top: 0.25rem;
}

.auth-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}

.auth-title-underline {
  width: 4rem;
  height: 2px;
  background: white;
  margin: 0 auto;
}

.auth-input {
  padding-left: 2.5rem;
  height: 3rem;
  background: transparent;
  border: 2px solid #9ca3af;
  color: white;
  border-radius: 0.5rem;
}

.auth-input::placeholder {
  color: #9ca3af;
}

.auth-input:focus {
  border-color: white;
  ring: 0;
}

.auth-button-primary {
  height: 3rem;
  background: #3b82f6;
  color: white;
  font-weight: 500;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.auth-button-primary:hover {
  background: #2563eb;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.auth-button-secondary {
  height: 3rem;
  background: white;
  color: #1e293b;
  font-weight: 500;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.auth-button-secondary:hover {
  background: #f1f5f9;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.auth-illustration {
  width: 20rem;
  height: 20rem;
  margin: 0 auto;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-illustration img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.loading-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid white;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
