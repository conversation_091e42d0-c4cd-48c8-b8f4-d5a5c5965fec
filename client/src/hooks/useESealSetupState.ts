import { useState, useEffect, useCallback } from 'react';
import type { SetupESealFormData } from '@shared';
import { ESealStateService, type ESealSetupState } from '../services/esealStateService';

export function useESealSetupState(esealId: string) {
  const [savedState, setSavedState] = useState<ESealSetupState | null>(null);
  const [hasExistingState, setHasExistingState] = useState(false);

  // Load saved state on mount
  useEffect(() => {
    const state = ESealStateService.getState(esealId);
    setSavedState(state);
    setHasExistingState(state !== null && !state.isCompleted);
  }, [esealId]);

  // Save state function
  const saveState = useCallback((
    formData: SetupESealFormData, 
    currentStep: number, 
    isCompleted: boolean = false
  ) => {
    ESealStateService.saveState(esealId, formData, currentStep, isCompleted);
    
    // Update local state
    const newState: ESealSetupState = {
      esealId,
      formData,
      currentStep,
      lastUpdated: new Date().toISOString(),
      isCompleted
    };
    setSavedState(newState);
    setHasExistingState(!isCompleted);
  }, [esealId]);

  // Remove state function
  const removeState = useCallback(() => {
    ESealStateService.removeState(esealId);
    setSavedState(null);
    setHasExistingState(false);
  }, [esealId]);

  // Mark as completed function
  const markCompleted = useCallback(() => {
    ESealStateService.markCompleted(esealId);
    if (savedState) {
      const updatedState = { ...savedState, isCompleted: true };
      setSavedState(updatedState);
    }
    setHasExistingState(false);
  }, [esealId, savedState]);

  return {
    savedState,
    hasExistingState,
    saveState,
    removeState,
    markCompleted
  };
}

export function useIncompleteSetups() {
  const [incompleteSetups, setIncompleteSetups] = useState<ESealSetupState[]>([]);

  const refreshIncompleteSetups = useCallback(() => {
    const setups = ESealStateService.getIncompleteSetups();
    setIncompleteSetups(setups);
  }, []);

  useEffect(() => {
    refreshIncompleteSetups();
  }, [refreshIncompleteSetups]);

  return {
    incompleteSetups,
    refreshIncompleteSetups
  };
}
