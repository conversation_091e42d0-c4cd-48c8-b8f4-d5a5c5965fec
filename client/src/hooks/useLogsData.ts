import { useState, useEffect } from 'react';

export interface LogActivity {
  id: string;
  esealId: string;
  esealName: string;
  esealIMEI: string;
  idVendor: string;
  timestamp: string;
  activity: string;
  location: {
    latitude: string | null;
    longitude: string | null;
    address: string | null;
    altitude: string | null;
    speed: string | null;
  };
  deviceInfo: {
    battery: string | null;
    dayaAki: string | null;
    event: string | null;
    kota: string | null;
    provinsi: string | null;
  };
  status: string;
}

export interface LogsResponse {
  success: boolean;
  data: {
    logs: LogActivity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface UseLogsDataProps {
  idVendor?: string;
  nomorESeal?: string;
  token?: string;
  page?: number;
  limit?: number;
}

export function useLogsData({ 
  idVendor = '', 
  nomorESeal = '', 
  token = '', 
  page = 1, 
  limit = 10 
}: UseLogsDataProps = {}) {
  const [data, setData] = useState<LogActivity[]>([]);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    // Don't fetch if no search criteria provided
    if (!idVendor && !nomorESeal && !token) {
      setData([]);
      setTotal(0);
      setTotalPages(0);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(idVendor && { idVendor }),
        ...(nomorESeal && { nomorESeal }),
        ...(token && { token }),
      });

      const response = await fetch(`/api/eseal/logs/search?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: LogsResponse = await response.json();
      
      if (!result.success) {
        throw new Error('Failed to fetch logs data');
      }

      setData(result.data.logs);
      setTotal(result.data.total);
      setTotalPages(result.data.totalPages);
    } catch (err) {
      console.error('Error fetching logs data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setData([]);
      setTotal(0);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [idVendor, nomorESeal, token, page, limit]);

  const refetch = () => {
    fetchData();
  };

  return {
    data,
    total,
    totalPages,
    loading,
    error,
    refetch,
  };
}
