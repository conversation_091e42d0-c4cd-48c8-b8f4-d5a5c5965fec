import type { SetupESealFormData } from '@shared';

const STORAGE_KEY = 'eseal_setup_state';

export interface ESealSetupState {
  esealId: string;
  formData: SetupESealFormData;
  currentStep: number;
  lastUpdated: string;
  isCompleted: boolean;
}

export class ESealStateService {
  /**
   * Save E-Seal setup state to localStorage
   */
  static saveState(esealId: string, formData: SetupESealFormData, currentStep: number, isCompleted: boolean = false): void {
    try {
      const state: ESealSetupState = {
        esealId,
        formData,
        currentStep,
        lastUpdated: new Date().toISOString(),
        isCompleted
      };

      const existingStates = this.getAllStates();
      existingStates[esealId] = state;

      localStorage.setItem(STORAGE_KEY, JSON.stringify(existingStates));
      console.log('💾 E-Seal setup state saved:', esealId);
    } catch (error) {
      console.error('❌ Failed to save E-Seal setup state:', error);
    }
  }

  /**
   * Get E-Seal setup state from localStorage
   */
  static getState(esealId: string): ESealSetupState | null {
    try {
      const states = this.getAllStates();
      return states[esealId] || null;
    } catch (error) {
      console.error('❌ Failed to get E-Seal setup state:', error);
      return null;
    }
  }

  /**
   * Get all saved E-Seal setup states
   */
  static getAllStates(): Record<string, ESealSetupState> {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('❌ Failed to get all E-Seal setup states:', error);
      return {};
    }
  }

  /**
   * Remove E-Seal setup state
   */
  static removeState(esealId: string): void {
    try {
      const states = this.getAllStates();
      delete states[esealId];
      localStorage.setItem(STORAGE_KEY, JSON.stringify(states));
      console.log('🗑️ E-Seal setup state removed:', esealId);
    } catch (error) {
      console.error('❌ Failed to remove E-Seal setup state:', error);
    }
  }

  /**
   * Clear all E-Seal setup states
   */
  static clearAllStates(): void {
    try {
      localStorage.removeItem(STORAGE_KEY);
      console.log('🧹 All E-Seal setup states cleared');
    } catch (error) {
      console.error('❌ Failed to clear E-Seal setup states:', error);
    }
  }

  /**
   * Check if E-Seal has saved state
   */
  static hasState(esealId: string): boolean {
    return this.getState(esealId) !== null;
  }

  /**
   * Get incomplete setups (for showing resume options)
   */
  static getIncompleteSetups(): ESealSetupState[] {
    try {
      const states = this.getAllStates();
      return Object.values(states).filter(state => !state.isCompleted);
    } catch (error) {
      console.error('❌ Failed to get incomplete setups:', error);
      return [];
    }
  }

  /**
   * Mark setup as completed
   */
  static markCompleted(esealId: string): void {
    try {
      const state = this.getState(esealId);
      if (state) {
        state.isCompleted = true;
        state.lastUpdated = new Date().toISOString();
        
        const states = this.getAllStates();
        states[esealId] = state;
        localStorage.setItem(STORAGE_KEY, JSON.stringify(states));
        
        console.log('✅ E-Seal setup marked as completed:', esealId);
      }
    } catch (error) {
      console.error('❌ Failed to mark setup as completed:', error);
    }
  }

  /**
   * Clean up old completed states (older than 7 days)
   */
  static cleanupOldStates(): void {
    try {
      const states = this.getAllStates();
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      let cleaned = false;
      Object.keys(states).forEach(esealId => {
        const state = states[esealId];
        if (state.isCompleted && new Date(state.lastUpdated) < sevenDaysAgo) {
          delete states[esealId];
          cleaned = true;
        }
      });

      if (cleaned) {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(states));
        console.log('🧹 Old completed E-Seal setup states cleaned up');
      }
    } catch (error) {
      console.error('❌ Failed to cleanup old states:', error);
    }
  }
}

// Auto cleanup on service load
ESealStateService.cleanupOldStates();
