import { createAuthClient } from "better-auth/react";
import { adminClient, organizationClient } from "better-auth/client/plugins";
import { ac, roles } from "./permissions";

export const authClient: any = createAuthClient({
  plugins: [
    adminClient({
      ac,
      roles,
    }),
    organizationClient({
      ac,
      roles,
    })
  ],
  // Use relative path for single origin deployment
  // baseURL will be automatically detected from current origin
});

export const { signIn, signUp, signOut, useSession, useActiveOrganization } = authClient;
