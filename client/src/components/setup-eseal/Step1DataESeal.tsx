import { FileCheck } from 'lucide-react';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import type { SetupESealFormData } from '@shared';
import { VENDOR_OPTIONS } from '@shared';
import { useOrganizations } from '@/hooks/useOrganizations';

interface Step1DataESealProps {
  formData: SetupESealFormData;
  setFormData: (data: SetupESealFormData) => void;
}

export function Step1DataESeal({ formData, setFormData }: Step1DataESealProps) {
  const { organizations, loading, error } = useOrganizations();

  return (
    <div>
      <div className="flex items-center mb-6">
        <FileCheck className="w-5 h-5 mr-2" />
        <h2 className="text-lg font-semibold text-gray-900">Data E-Seal</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

        {/* Organization */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Organisasi <span className="text-red-500">*</span>
          </label>
          <Select
            value={formData.organizationId}
            onValueChange={(value) => setFormData({ ...formData, organizationId: value })}
            disabled={loading}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Pilih organisasi" />
            </SelectTrigger>
            <SelectContent>
              {error && <SelectItem value="error" disabled>{error}</SelectItem>}
              {!loading && !error && organizations.map((org) => (
                <SelectItem key={org.id} value={org.id}>
                  {org.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-gray-500 mt-1">Pilih organisasi yang akan menggunakan E-Seal ini</p>
        </div>

        {/* ID Vendor */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            ID Vendor <span className="text-red-500">*</span>
          </label>
          <Select
            value={formData.idVendor}
            onValueChange={(value) => setFormData({...formData, idVendor: value})}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Pilih vendor E-Seal" />
            </SelectTrigger>
            <SelectContent>
              {VENDOR_OPTIONS.map((vendor) => (
                <SelectItem key={vendor.value} value={vendor.value}>
                  {vendor.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-gray-500 mt-1">Pilih vendor penyedia E-Seal</p>
        </div>



        {/* Nomor E-Seal */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nomor E-Seal <span className="text-red-500">*</span>
          </label>
          <Input
            value={formData.noEseal}
            onChange={(e) => setFormData({...formData, noEseal: e.target.value})}
            className="w-full"
            placeholder="Nomor unik E-Seal"
          />
          <p className="text-xs text-gray-500 mt-1">Nomor identifikasi unik E-Seal</p>
        </div>

        {/* Nomor IMEI */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nomor IMEI <span className="text-red-500">*</span>
          </label>
          <Input
            value={formData.noImei}
            onChange={(e) => setFormData({...formData, noImei: e.target.value})}
            className="w-full"
            placeholder="15 digit IMEI E-Seal"
          />
          <p className="text-xs text-gray-500 mt-1">Nomor IMEI 15 digit dari perangkat E-Seal</p>
        </div>


        {/* Token */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Token <span className="text-gray-500">(opsional)</span>
          </label>
          <Input
            placeholder="Token vendor (jika diperlukan)"
            value={formData.token}
            onChange={(e) => setFormData({...formData, token: e.target.value})}
            className="w-full"
          />
          <p className="text-xs text-gray-500 mt-1">Token khusus dari vendor (opsional)</p>
        </div>
      </div>

      {/* Info Box */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start">
          <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
            <span className="text-xs font-medium text-blue-600">i</span>
          </div>
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">Informasi Penting:</p>
            <ul className="text-xs space-y-1">
              <li>• Pastikan nomor IMEI sesuai dengan perangkat E-Seal fisik</li>
              <li>• Alamat asal dan tujuan harus lengkap dan akurat</li>
              <li>• ID Vendor harus sesuai dengan penyedia E-Seal yang digunakan</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
