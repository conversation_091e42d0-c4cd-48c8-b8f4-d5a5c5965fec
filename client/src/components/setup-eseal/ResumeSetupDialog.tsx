import { useState } from 'react';
import { Clock, Play, Trash2, X } from 'lucide-react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { useIncompleteSetups } from '../../hooks/useESealSetupState';
import { ESealStateService } from '../../services/esealStateService';

interface ResumeSetupDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onResume: (esealId: string, step: number) => void;
}

export function ResumeSetupDialog({ isOpen, onClose, onResume }: ResumeSetupDialogProps) {
  const { incompleteSetups, refreshIncompleteSetups } = useIncompleteSetups();
  const [deletingId, setDeletingId] = useState<string | null>(null);

  if (!isOpen) return null;

  const handleDelete = async (esealId: string) => {
    setDeletingId(esealId);
    try {
      ESealStateService.removeState(esealId);
      refreshIncompleteSetups();
    } catch (error) {
      console.error('Failed to delete setup state:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const handleResume = (esealId: string, step: number) => {
    onResume(esealId, step);
    onClose();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStepName = (step: number) => {
    switch (step) {
      case 1: return 'Informasi Dasar';
      case 2: return 'Lokasi';
      case 3: return 'Kendaraan & Driver';
      case 4: return 'Dokumen';
      case 5: return 'Review';
      default: return `Step ${step}`;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Clock className="w-5 h-5 mr-2 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">
              Lanjutkan Setup E-Seal
            </h2>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {incompleteSetups.length === 0 ? (
            <div className="text-center py-8">
              <Clock className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">Tidak ada setup yang belum selesai</p>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-gray-600 mb-4">
                Anda memiliki {incompleteSetups.length} setup E-Seal yang belum selesai. 
                Pilih salah satu untuk melanjutkan:
              </p>

              <div className="space-y-3 max-h-96 overflow-y-auto">
                {incompleteSetups.map((setup) => (
                  <div
                    key={setup.esealId}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {setup.formData.noEseal || 'E-Seal Baru'}
                        </h3>
                        <Badge variant="outline" className="text-xs">
                          {getStepName(setup.currentStep)}
                        </Badge>
                      </div>
                      
                      <div className="text-xs text-gray-500 space-y-1">
                        {setup.formData.noImei && (
                          <p>IMEI: {setup.formData.noImei}</p>
                        )}
                        {setup.formData.noKontainer && (
                          <p>Kontainer: {setup.formData.noKontainer}</p>
                        )}
                        <p>Terakhir diubah: {formatDate(setup.lastUpdated)}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleResume(setup.esealId, setup.currentStep)}
                        className="flex items-center"
                      >
                        <Play className="w-3 h-3 mr-1" />
                        Lanjutkan
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDelete(setup.esealId)}
                        disabled={deletingId === setup.esealId}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <Button variant="outline" onClick={onClose}>
            Tutup
          </Button>
        </div>
      </div>
    </div>
  );
}
