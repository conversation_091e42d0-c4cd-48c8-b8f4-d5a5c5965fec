import { MapPin, Map, Navigation, Search } from 'lucide-react';
import { Input } from '../ui/input';
import type { SetupESealFormData } from '@shared';
import { useState, useEffect } from 'react';
import MapTiler from '../map/MapTiler';

interface Step2LokasiProps {
  formData: SetupESealFormData;
  setFormData: (data: SetupESealFormData) => void;
}

interface SearchResult {
  lat: string;
  lon: string;
  display_name: string;
  addressdetails?: Record<string, string | number>;
}

export function Step2Lokasi({ formData, setFormData }: Step2LokasiProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [activeSelection, setActiveSelection] = useState<'origin' | 'destination'>('origin');

  // Convert form data to location objects
  const originLocation = formData.latitudeAsal && formData.longitudeAsal ? {
    lat: parseFloat(formData.latitudeAsal),
    lng: parseFloat(formData.longitudeAsal),
    address: formData.lokasiAsal
  } : undefined;

  const destinationLocation = formData.latitudeTujuan && formData.longitudeTujuan ? {
    lat: parseFloat(formData.latitudeTujuan),
    lng: parseFloat(formData.longitudeTujuan),
    address: formData.lokasiTujuan
  } : undefined;

  // Handle location selection from map
  const handleOriginSelect = (location: { lat: number; lng: number; address?: string }) => {
    console.log('handleOriginSelect called:', location);
    const newFormData = {
      ...formData,
      latitudeAsal: location.lat.toString(),
      longitudeAsal: location.lng.toString(),
      lokasiAsal: location.address || formData.lokasiAsal,
      alamatAsal: location.address || formData.alamatAsal
    };
    console.log('New form data:', newFormData);
    setFormData(newFormData);
  };

  const handleDestinationSelect = (location: { lat: number; lng: number; address?: string }) => {
    console.log('handleDestinationSelect called:', location);
    const newFormData = {
      ...formData,
      latitudeTujuan: location.lat.toString(),
      longitudeTujuan: location.lng.toString(),
      lokasiTujuan: location.address || formData.lokasiTujuan,
      alamatTujuan: location.address || formData.alamatTujuan
    };
    console.log('New form data:', newFormData);
    setFormData(newFormData);
  };

  // Search for locations using MapTiler
  const searchLocation = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch(
        `https://api.maptiler.com/geocoding/${encodeURIComponent(query)}.json?key=a7S8ykFBG9qI73UllxnL&country=ID&limit=5&language=id`
      );
      const data = await response.json();
      const results = data.features.map((feature: any) => ({
        lat: feature.center[1].toString(),
        lon: feature.center[0].toString(),
        display_name: feature.place_name,
      }));
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching location:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchQuery) {
        searchLocation(searchQuery);
      } else {
        setSearchResults([]);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);



  return (
    <div>
      <div className="flex items-center mb-6">
        <MapPin className="w-5 h-5 mr-2" />
        <h2 className="text-lg font-semibold text-gray-900">Lokasi & Koordinat</h2>
      </div>

      <div className="space-y-6">
        {/* Search Lokasi */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Search className="w-4 h-4 inline mr-1" />
            Cari Lokasi
          </label>
          <div className="relative">
            <Input
              placeholder="Cari alamat, kota, landmark di Indonesia..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
            />

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                <div className="p-2 text-xs text-gray-500 border-b">
                  Pilih untuk lokasi mana:
                </div>
                {searchResults.map((result, index) => (
                  <div key={index} className="border-b border-gray-100 last:border-b-0">
                    <div className="px-3 py-1 text-xs font-medium text-gray-700 bg-gray-50">
                      {result.display_name}
                    </div>
                    <div className="flex">
                      <button
                        onClick={() => {
                          const location = {
                            lat: parseFloat(result.lat),
                            lng: parseFloat(result.lon),
                            address: result.display_name
                          };
                          handleOriginSelect(location);
                          setSearchQuery('');
                          setSearchResults([]);
                        }}
                        className="flex-1 px-3 py-2 text-left text-sm hover:bg-red-50 border-r border-gray-100"
                      >
                        <span className="text-red-600">📍 Set sebagai Asal</span>
                      </button>
                      <button
                        onClick={() => {
                          const location = {
                            lat: parseFloat(result.lat),
                            lng: parseFloat(result.lon),
                            address: result.display_name
                          };
                          handleDestinationSelect(location);
                          setSearchQuery('');
                          setSearchResults([]);
                        }}
                        className="flex-1 px-3 py-2 text-left text-sm hover:bg-green-50"
                      >
                        <span className="text-green-600">🎯 Set sebagai Tujuan</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {isSearching && (
              <div className="absolute right-3 top-2.5">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
              </div>
            )}
          </div>
        </div>

        {/* Lokasi yang Dipilih */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Lokasi Asal */}
          <div className="p-4 border border-red-200 rounded-lg bg-red-50">
            <div className="flex items-center mb-2">
              <span className="text-red-500 mr-2">📍</span>
              <h3 className="font-medium text-red-800">Lokasi Asal</h3>
            </div>
            {originLocation ? (
              <div>
                <div className="text-sm text-red-700 mb-1">{originLocation.address}</div>
                <div className="text-xs text-red-600">
                  {originLocation.lat.toFixed(6)}, {originLocation.lng.toFixed(6)}
                </div>
              </div>
            ) : (
              <div className="text-sm text-red-600">Belum dipilih</div>
            )}
          </div>

          {/* Lokasi Tujuan */}
          <div className="p-4 border border-green-200 rounded-lg bg-green-50">
            <div className="flex items-center mb-2">
              <span className="text-green-500 mr-2">🎯</span>
              <h3 className="font-medium text-green-800">Lokasi Tujuan</h3>
            </div>
            {destinationLocation ? (
              <div>
                <div className="text-sm text-green-700 mb-1">{destinationLocation.address}</div>
                <div className="text-xs text-green-600">
                  {destinationLocation.lat.toFixed(6)}, {destinationLocation.lng.toFixed(6)}
                </div>
              </div>
            ) : (
              <div className="text-sm text-green-600">Belum dipilih</div>
            )}
          </div>
        </div>

        {/* Peta Interaktif */}
        <div>
          <div className="flex items-center mb-4">
            <Map className="w-5 h-5 mr-2" />
            <h3 className="text-base font-medium text-gray-900">Peta Interaktif</h3>
          </div>

          <MapTiler
            originLocation={originLocation}
            destinationLocation={destinationLocation}
            onOriginSelect={handleOriginSelect}
            onDestinationSelect={handleDestinationSelect}
            height="500px"
          />
        </div>

        {/* Petunjuk */}
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start">
            <Navigation className="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
            <div className="text-xs text-blue-800">
              <p className="font-medium mb-1">Petunjuk:</p>
              <ul className="space-y-1">
                <li>• Klik tombol <Map className="w-3 h-3 inline mx-1" /> untuk membuka peta interaktif</li>
                <li>• Gunakan search di peta untuk mencari alamat atau landmark</li>
                <li>• Klik pada peta untuk menentukan koordinat dengan presisi</li>
                <li>• Koordinat akan otomatis terisi saat memilih lokasi di peta</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Status Koordinat */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center mr-2">
              <span className="text-xs font-medium text-gray-600">
                {formData.latitudeAsal && formData.longitudeAsal && formData.latitudeTujuan && formData.longitudeTujuan ? '✓' : '!'}
              </span>
            </div>
            <h4 className="text-sm font-medium text-gray-900">
              {formData.latitudeAsal && formData.longitudeAsal && formData.latitudeTujuan && formData.longitudeTujuan
                ? 'Koordinat lengkap'
                : 'Koordinat belum lengkap'
              }
            </h4>
          </div>
          <p className="text-xs text-gray-600 ml-8">
            {formData.latitudeAsal && formData.longitudeAsal && formData.latitudeTujuan && formData.longitudeTujuan
              ? 'Semua koordinat telah diisi. Anda dapat melanjutkan ke langkah berikutnya.'
              : 'Lengkapi koordinat asal dan tujuan untuk melanjutkan.'
            }
          </p>
        </div>
      </div>
    </div>
  );
}
