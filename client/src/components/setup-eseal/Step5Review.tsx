import { Rotate<PERSON><PERSON><PERSON>, <PERSON><PERSON>he<PERSON>, MapPin, Truck, CheckCircle } from 'lucide-react';
import type { SetupESealFormData } from '@shared';

interface Step5ReviewProps {
  formData: SetupESealFormData;
}

export function Step5Review({ formData }: Step5ReviewProps) {
  return (
    <div>
      <div className="flex items-center mb-6">
        <RotateCcw className="w-5 h-5 mr-2" />
        <h2 className="text-lg font-semibold text-gray-900">Review Data Pengiriman</h2>
      </div>

      <div className="space-y-6">
        {/* Review Header */}
        <div>
          <h3 className="text-base font-medium text-gray-900 mb-2">Review Data Pengiriman</h3>
          <p className="text-sm text-gray-600">
            Periksa kembali semua data sebelum memulai monitoring
          </p>
        </div>

        {/* Review Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-6">
            {/* Data E-Seal */}
            <div>
              <div className="flex items-center mb-3">
                <FileCheck className="w-4 h-4 mr-2 text-gray-600" />
                <h4 className="text-sm font-medium text-gray-900">Data E-Seal</h4>
              </div>
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">IMEI:</span>
                  <span className="font-medium">{formData.noImei}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Nomor E-Seal:</span>
                  <span className="font-medium">{formData.noEseal}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">ID Vendor:</span>
                  <span className="font-medium">{formData.idVendor || '-'}</span>
                </div>
              </div>
            </div>

            {/* Lokasi */}
            <div>
              <div className="flex items-center mb-3">
                <MapPin className="w-4 h-4 mr-2 text-gray-600" />
                <h4 className="text-sm font-medium text-gray-900">Lokasi & Koordinat</h4>
              </div>
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div>
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Asal</span>
                  <p className="text-sm text-gray-900 mt-1">
                    {formData.lokasiAsal || formData.alamatAsal}
                  </p>
                  <p className="text-xs text-gray-600">
                    Koordinat: {formData.latitudeAsal}, {formData.longitudeAsal}
                  </p>
                </div>
                <div>
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Tujuan</span>
                  <p className="text-sm text-gray-900 mt-1">
                    {formData.lokasiTujuan || formData.alamatTujuan}
                  </p>
                  <p className="text-xs text-gray-600">
                    Koordinat: {formData.latitudeTujuan}, {formData.longitudeTujuan}
                  </p>
                </div>
              </div>
            </div>

            {/* Kendaraan & Driver */}
            <div>
              <div className="flex items-center mb-3">
                <Truck className="w-4 h-4 mr-2 text-gray-600" />
                <h4 className="text-sm font-medium text-gray-900">Kendaraan & Driver</h4>
              </div>
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Nomor Polisi:</span>
                  <span className="font-medium">{formData.noPolisi || '-'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Kontainer:</span>
                  <span className="font-medium">
                    {formData.jnsKontainer} - {formData.ukKontainer}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">No. Kontainer:</span>
                  <span className="font-medium">{formData.noKontainer || '-'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Driver:</span>
                  <span className="font-medium">{formData.namaDriver || '-'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">No. Tlp Driver:</span>
                  <span className="font-medium">{formData.nomorTeleponDriver || '-'}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Dokumen AJU */}
            <div>
              <div className="flex items-center mb-3">
                <CheckCircle className="w-4 h-4 mr-2 text-gray-600" />
                <h4 className="text-sm font-medium text-gray-900">Dokumen AJU</h4>
              </div>
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Nomor AJU:</span>
                  <span className="font-medium">{formData.nomorAJU || 'N/A'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Status Validasi:</span>
                  <span className={`font-medium ${
                    formData.statusValidasi === 'valid' ? 'text-green-600' :
                    formData.statusValidasi === 'invalid' ? 'text-red-600' :
                    'text-yellow-600'
                  }`}>
                    {formData.statusValidasi === 'valid' ? 'Validasi Berhasil' :
                     formData.statusValidasi === 'invalid' ? 'Validasi Gagal' :
                     'Belum Divalidasi'}
                  </span>
                </div>
              </div>
            </div>

            {/* Dokumen Details */}
            {formData.dokumen.length > 0 && (
              <div>
                <div className="flex items-center mb-3">
                  <FileCheck className="w-4 h-4 mr-2 text-gray-600" />
                  <h4 className="text-sm font-medium text-gray-900">Detail Dokumen</h4>
                </div>
                <div className="space-y-3">
                  {formData.dokumen.map((dok, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <h5 className="text-sm font-medium text-gray-900 mb-2">Dokumen {index + 1}</h5>
                      <div className="space-y-1 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Kode Dokumen:</span>
                          <span className="font-medium">{dok.kodeDokumen}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Kode Kantor:</span>
                          <span className="font-medium">{dok.kodeKantor}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Nomor Dokumen:</span>
                          <span className="font-medium">{dok.nomorDokumen}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Tanggal:</span>
                          <span className="font-medium">{dok.tanggalDokumen}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Jenis Muat:</span>
                          <span className="font-medium">{dok.jenisMuat}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Jumlah Kontainer:</span>
                          <span className="font-medium">{dok.jumlahKontainer}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* API Mapping Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                  <span className="text-xs font-medium text-blue-600">i</span>
                </div>
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">Integrasi API Beacukai:</p>
                  <ul className="text-xs space-y-1">
                    <li>• Data akan dikirim ke endpoint tracking/start</li>
                    <li>• JWT token akan diambil otomatis dari sistem</li>
                    <li>• Semua field telah disesuaikan dengan format API</li>
                    <li>• Monitoring real-time akan dimulai setelah submit</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Final Confirmation */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-start">
            <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-green-900 mb-1">Siap untuk Monitoring</h4>
              <p className="text-xs text-green-800">
                Semua data telah lengkap dan terverifikasi. Klik "Mulai Monitoring" untuk memulai pelacakan E-Seal secara real-time melalui API Beacukai.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
