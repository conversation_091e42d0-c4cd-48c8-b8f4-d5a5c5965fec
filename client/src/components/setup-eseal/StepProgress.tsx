import React from 'react';

interface Step {
  id: number;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  active: boolean;
}

interface StepProgressProps {
  currentStep: number;
  steps: Step[];
}

export function StepProgress({ currentStep, steps }: StepProgressProps) {
  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <span className="text-sm text-gray-600">Langkah {currentStep} dari {steps.length}</span>
      </div>
      
      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
        <div 
          className="bg-green-500 h-2 rounded-full transition-all duration-300"
          style={{ width: `${(currentStep / steps.length) * 100}%` }}
        ></div>
      </div>

      {/* Step Icons */}
      <div className="flex justify-between items-center">
        {steps.map((step) => {
          const Icon = step.icon;
          const isActive = currentStep >= step.id;
          const isCurrent = currentStep === step.id;
          
          return (
            <div key={step.id} className="flex flex-col items-center">
              <div className={`
                w-12 h-12 rounded-full flex items-center justify-center mb-2 transition-colors
                ${isActive 
                  ? 'bg-slate-800 text-white' 
                  : 'bg-gray-200 text-gray-400'
                }
                ${isCurrent ? 'ring-4 ring-slate-200' : ''}
              `}>
                <Icon className="w-5 h-5" />
              </div>
              <span className={`text-xs text-center max-w-16 leading-tight ${
                isActive ? 'text-gray-900 font-medium' : 'text-gray-400'
              }`}>
                {step.title}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}
