import { X } from 'lucide-react';
import { Button } from '../ui/button';

interface StopModalProps {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export function StopModal({ isOpen, onConfirm, onCancel }: StopModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Konfirmasi Berhenti Monitoring
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Modal Body */}
        <div className="p-6">
          <p className="text-gray-600 text-sm leading-relaxed">
            Apakah Anda yakin ingin menghentikan setup monitoring E-Seal ini? 
            Data yang telah diisi akan hilang dan Anda akan kembali ke halaman utama.
          </p>
        </div>

        {/* Modal Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={onCancel}
            className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            Batal
          </Button>
          <Button
            onClick={onConfirm}
            className="px-6 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Ya, Hentikan
          </Button>
        </div>
      </div>
    </div>
  );
}
