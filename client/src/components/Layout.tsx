import { ReactNode, useState } from 'react';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { Button } from './ui/button';
import { Menu } from 'lucide-react';

interface LayoutProps {
  children: ReactNode;
  activeMenu: string;
  onMenuClick: (menu: string) => void;
}

export function Layout({ children, activeMenu, onMenuClick }: LayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 to-slate-200">
      {/* Fixed Header for Desktop */}
      <Header onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)} />

      {/* Mobile Header with Hamburger */}
      <div className="fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200 px-4 py-3 shadow-sm lg:hidden">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="flex items-center"
        >
          <Menu className="w-5 h-5" />
          <span className="ml-2 text-sm font-medium">Menu</span>
        </Button>
      </div>

      {/* Main Content Area with proper spacing from fixed header */}
      <div className="pt-16 pb-4 px-4 md:pt-20 md:pb-6 md:px-6 lg:pt-24 lg:pb-8 lg:px-8 min-h-screen">
        <div className="flex flex-col lg:flex-row gap-4 md:gap-6 h-[calc(100vh-4rem)] md:h-[calc(100vh-5rem)] lg:h-[calc(100vh-8rem)] max-w-full mx-auto">
          {/* Desktop Sidebar - Floating Container */}
          <div className="hidden lg:block w-64 flex-shrink-0">
            <Sidebar activeMenu={activeMenu} onMenuClick={onMenuClick} />
          </div>

          {/* Mobile Sidebar Overlay */}
          {isMobileMenuOpen && (
            <div className="fixed top-16 left-0 right-0 bottom-0 z-50 lg:hidden">
              <div
                className="absolute inset-0 bg-black/50 backdrop-blur-sm"
                onClick={() => setIsMobileMenuOpen(false)}
              />
              <div className="absolute top-0 left-0 w-64 h-full bg-slate-800 rounded-r-xl shadow-2xl">
                <div className="relative h-full">
                  {/* Close button positioned at top right of sidebar */}
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="absolute top-4 right-4 text-white hover:text-gray-300 transition-colors z-20 bg-slate-700 rounded-full p-1"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>

                  {/* Sidebar content with padding to avoid close button */}
                  <div className="h-full pt-4 pr-12 pl-4 pb-4">
                    <Sidebar
                      activeMenu={activeMenu}
                      onMenuClick={(menu) => {
                        onMenuClick(menu);
                        setIsMobileMenuOpen(false);
                      }}
                      isMobile={true}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Main Content - Floating Container with fixed dimensions and width constraints */}
          <main className="flex-1 h-full min-h-0 min-w-0">
            <div className="bg-white rounded-xl shadow-lg h-full flex flex-col w-full">
              <div className="flex-1 overflow-hidden p-4 md:p-6 lg:p-8">
                <div className="h-full overflow-y-auto">
                  {children}
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}
