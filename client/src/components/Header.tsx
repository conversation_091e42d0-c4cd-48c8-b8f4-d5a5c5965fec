import { User, LogOut } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from './ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { useSession, signOut } from '@/lib/auth-client';

interface HeaderProps {
  onMobileMenuToggle?: () => void;
}

export function Header({ }: HeaderProps) {
  const navigate = useNavigate();
  const { data: session } = useSession();

  const handleLogout = async () => {
    try {
      await signOut();
      
      
    } catch (error) {
      console.error('Logout error:', error);
      // Fallback: navigate to login anyway
      
      
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200 px-6 py-4 shadow-sm hidden lg:block">
      <div className="flex items-center justify-between">
        {/* Logo */}
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center shadow-md">
            <span className="text-white font-bold text-sm">VIT</span>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-900">VIT-PLI</div>
            <div className="text-xs text-gray-500">Vessel Tracking & Monitoring</div>
          </div>
        </div>

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center space-x-2">
              <span className="text-sm font-medium">{session?.user?.name || 'User'}</span>
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-gray-600" />
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>Profile</DropdownMenuItem>
            <DropdownMenuItem>Settings</DropdownMenuItem>
            <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
              <LogOut className="w-4 h-4 mr-2" />
              Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
