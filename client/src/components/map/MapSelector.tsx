import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import { Icon } from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in React Leaflet
import markerIcon from 'leaflet/dist/images/marker-icon.png';
import markerIcon2x from 'leaflet/dist/images/marker-icon-2x.png';
import markerShadow from 'leaflet/dist/images/marker-shadow.png';

// Create custom icons
const originIcon = new Icon({
  iconUrl: markerIcon,
  iconRetinaUrl: markerIcon2x,
  shadowUrl: markerShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const destinationIcon = new Icon({
  iconUrl: markerIcon,
  iconRetinaUrl: markerIcon2x,
  shadowUrl: markerShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

interface LocationData {
  lat: number;
  lng: number;
  address?: string;
}

interface MapSelectorProps {
  originLocation?: LocationData;
  destinationLocation?: LocationData;
  onOriginSelect: (location: LocationData) => void;
  onDestinationSelect: (location: LocationData) => void;
  mode: 'origin' | 'destination';
  height?: string;
}

// Component to handle map clicks
function MapClickHandler({
  onLocationSelect
}: {
  onLocationSelect: (location: LocationData) => void;
}) {
  useMapEvents({
    click: async (e) => {
      const { lat, lng } = e.latlng;
      
      // Reverse geocoding to get address
      try {
        const response = await fetch(
          `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`
        );
        const data = await response.json();
        
        onLocationSelect({
          lat,
          lng,
          address: data.display_name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`
        });
      } catch (error) {
        console.error('Error getting address:', error);
        onLocationSelect({
          lat,
          lng,
          address: `${lat.toFixed(6)}, ${lng.toFixed(6)}`
        });
      }
    },
  });

  return null;
}

export function MapSelector({
  originLocation,
  destinationLocation,
  onOriginSelect,
  onDestinationSelect,
  mode,
  height = '400px'
}: MapSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Default center to Jakarta, Indonesia
  const defaultCenter: [number, number] = [-6.2088, 106.8456];
  
  // Determine map center based on existing locations or default
  const mapCenter: [number, number] = 
    originLocation ? [originLocation.lat, originLocation.lng] :
    destinationLocation ? [destinationLocation.lat, destinationLocation.lng] :
    defaultCenter;

  // Search for locations using Nominatim
  const searchLocation = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&countrycodes=id&limit=5&addressdetails=1`
      );
      const data = await response.json();
      setSearchResults(data);
    } catch (error) {
      console.error('Error searching location:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle search input with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      searchLocation(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const handleLocationSelect = (location: LocationData) => {
    if (mode === 'origin') {
      onOriginSelect(location);
    } else {
      onDestinationSelect(location);
    }
  };

  const selectSearchResult = (result: any) => {
    const location: LocationData = {
      lat: parseFloat(result.lat),
      lng: parseFloat(result.lon),
      address: result.display_name
    };
    
    handleLocationSelect(location);
    setSearchQuery('');
    setSearchResults([]);
  };

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <input
          type="text"
          placeholder={`Cari lokasi ${mode === 'origin' ? 'asal' : 'tujuan'}...`}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        
        {/* Search Results */}
        {searchResults.length > 0 && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {searchResults.map((result, index) => (
              <button
                key={index}
                onClick={() => selectSearchResult(result)}
                className="w-full px-4 py-2 text-left hover:bg-gray-100 border-b border-gray-100 last:border-b-0"
              >
                <div className="font-medium text-sm">{result.display_name}</div>
                <div className="text-xs text-gray-500">
                  {parseFloat(result.lat).toFixed(6)}, {parseFloat(result.lon).toFixed(6)}
                </div>
              </button>
            ))}
          </div>
        )}
        
        {isSearching && (
          <div className="absolute right-3 top-2.5">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
          </div>
        )}
      </div>

      {/* Map Container */}
      <div style={{ height }} className="rounded-lg overflow-hidden border border-gray-300">
        <MapContainer
          center={mapCenter}
          zoom={13}
          style={{ height: '100%', width: '100%' }}
          scrollWheelZoom={true}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          
          {/* Map Click Handler */}
          <MapClickHandler
            onLocationSelect={handleLocationSelect}
          />
          
          {/* Origin Marker */}
          {originLocation && (
            <Marker 
              position={[originLocation.lat, originLocation.lng]} 
              icon={originIcon}
            >
              <Popup>
                <div>
                  <strong>Lokasi Asal</strong><br />
                  {originLocation.address}<br />
                  <small>{originLocation.lat.toFixed(6)}, {originLocation.lng.toFixed(6)}</small>
                </div>
              </Popup>
            </Marker>
          )}
          
          {/* Destination Marker */}
          {destinationLocation && (
            <Marker 
              position={[destinationLocation.lat, destinationLocation.lng]} 
              icon={destinationIcon}
            >
              <Popup>
                <div>
                  <strong>Lokasi Tujuan</strong><br />
                  {destinationLocation.address}<br />
                  <small>{destinationLocation.lat.toFixed(6)}, {destinationLocation.lng.toFixed(6)}</small>
                </div>
              </Popup>
            </Marker>
          )}
        </MapContainer>
      </div>

      {/* Instructions */}
      <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
        <p className="font-medium mb-1">Cara menggunakan peta:</p>
        <ul className="space-y-1 text-xs">
          <li>• Gunakan kotak pencarian untuk mencari lokasi</li>
          <li>• Klik pada peta untuk memilih koordinat secara manual</li>
          <li>• Drag peta untuk menjelajahi area lain</li>
          <li>• Zoom in/out menggunakan tombol + dan - atau scroll mouse</li>
        </ul>
      </div>
    </div>
  );
}
