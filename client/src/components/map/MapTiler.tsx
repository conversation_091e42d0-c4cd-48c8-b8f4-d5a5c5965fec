import React, { useRef, useEffect } from 'react';
import * as maptilersdk from '@maptiler/sdk';
import "@maptiler/sdk/dist/maptiler-sdk.css";
import './MapTiler.css';

maptilersdk.config.apiKey = 'a7S8ykFBG9qI73UllxnL';

interface MapTilerProps {
    originLocation?: { lat: number; lng: number; address?: string };
    destinationLocation?: { lat: number; lng: number; address?: string };
    onOriginSelect?: (location: { lat: number; lng: number; address?: string }) => void;
    onDestinationSelect?: (location: { lat: number; lng: number; address?: string }) => void;
    height?: string;
    interactive?: boolean;
}

export default function MapTiler({
    originLocation,
    destinationLocation,
    onOriginSelect,
    onDestinationSelect,
    height = '100%',
    interactive = true,
}: MapTilerProps) {
    const mapContainer = useRef(null);
    const map = useRef<maptilersdk.Map | null>(null);
    const originMarker = useRef<maptilersdk.Marker | null>(null);
    const destinationMarker = useRef<maptilersdk.Marker | null>(null);

    useEffect(() => {
        if (map.current || !mapContainer.current) return;

        const initialState = {
            lng: originLocation?.lng || destinationLocation?.lng || 118.0149,
            lat: originLocation?.lat || destinationLocation?.lat || -2.5489,
            zoom: 4,
        };

       

        try {
            map.current = new maptilersdk.Map({
                container: mapContainer.current,
                style: maptilersdk.MapStyle.STREETS,
                center: [initialState.lng, initialState.lat],
                zoom: initialState.zoom,
                interactive: interactive,
            });

            map.current.on('load', () => {
                console.log('MapTiler map loaded successfully');
            });

            map.current.on('error', (e) => {
                console.error('MapTiler error:', e);
            });

            map.current.on('styleimagemissing', (e) => {
                console.warn('MapTiler style image missing:', e.id);
            });
        } catch (error) {
            console.error('Error initializing MapTiler:', error);
        }

        if (interactive && onOriginSelect && map.current) {
            map.current.on('click', async (e) => {
                const { lng, lat } = e.lngLat;
                try {
                    const response = await fetch(
                        `https://api.maptiler.com/geocoding/${lng},${lat}.json?key=${maptilersdk.config.apiKey}`
                    );
                    const data = await response.json();
                    const address = data.features[0]?.place_name || `Unknown location at ${lat.toFixed(4)}, ${lng.toFixed(4)}`;

                    // Simple logic to decide if it's origin or destination
                    // This can be improved with a UI toggle
                    if (!originLocation || (destinationLocation && onDestinationSelect)) {
                        onOriginSelect({ lat, lng, address });
                    } else if(onDestinationSelect) {
                        onDestinationSelect({ lat, lng, address });
                    }

                } catch (error) {
                    console.error("Error reverse geocoding:", error);
                    onOriginSelect({ lat, lng, address: `Unknown location at ${lat.toFixed(4)}, ${lng.toFixed(4)}` });
                }
            });
        }

    }, [interactive, onOriginSelect, onDestinationSelect, originLocation, destinationLocation]);

    useEffect(() => {
        if (!map.current) return;

        // Update Origin Marker
        if (originLocation) {
            if (originMarker.current) {
                originMarker.current.setLngLat([originLocation.lng, originLocation.lat]);
            } else {
                originMarker.current = new maptilersdk.Marker({ color: "#FF0000" })
                    .setLngLat([originLocation.lng, originLocation.lat])
                    .addTo(map.current);
            }
        } else if (originMarker.current) {
            originMarker.current.remove();
            originMarker.current = null;
        }

        // Update Destination Marker
        if (destinationLocation) {
            if (destinationMarker.current) {
                destinationMarker.current.setLngLat([destinationLocation.lng, destinationLocation.lat]);
            } else {
                destinationMarker.current = new maptilersdk.Marker({ color: "#00FF00" })
                    .setLngLat([destinationLocation.lng, destinationLocation.lat])
                    .addTo(map.current);
            }
        } else if (destinationMarker.current) {
            destinationMarker.current.remove();
            destinationMarker.current = null;
        }

        // Fit bounds if both locations are present
        if (originLocation && destinationLocation) {
            const bounds = new maptilersdk.LngLatBounds();
            bounds.extend([originLocation.lng, originLocation.lat]);
            bounds.extend([destinationLocation.lng, destinationLocation.lat]);
            map.current.fitBounds(bounds, { padding: 100 });
        } else if (originLocation) {
            map.current.flyTo({ center: [originLocation.lng, originLocation.lat], zoom: 14 });
        } else if (destinationLocation) {
            map.current.flyTo({ center: [destinationLocation.lng, destinationLocation.lat], zoom: 14 });
        }


    }, [originLocation, destinationLocation]);


    return (
        <div className="map-wrap" style={{ height }}>
            <div ref={mapContainer} className="map" />
        </div>
    );
}
