import React, { useRef, useEffect, useState } from 'react';
import * as maptilersdk from '@maptiler/sdk';
import "@maptiler/sdk/dist/maptiler-sdk.css";
import { Button } from '../ui/button';
import { Play, Pause, Square, SkipBack, SkipForward } from 'lucide-react';

maptilersdk.config.apiKey = 'a7S8ykFBG9qI73UllxnL';

interface TrackPoint {
  lat: number;
  lng: number;
  timestamp: string;
  speed?: number;
}

interface MapTilerReplayProps {
  trackData: TrackPoint[];
  height?: string;
  startLocation?: { lat: number; lng: number; address?: string };
  endLocation?: { lat: number; lng: number; address?: string };
}

export default function MapTilerReplay({
  trackData,
  height = '500px',
  startLocation,
  endLocation,
}: MapTilerReplayProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<maptilersdk.Map | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [routeCoordinates, setRouteCoordinates] = useState<number[][]>([]);
  const animationRef = useRef<number | null>(null);
  const currentMarker = useRef<maptilersdk.Marker | null>(null);
  const startMarker = useRef<maptilersdk.Marker | null>(null);
  const endMarker = useRef<maptilersdk.Marker | null>(null);

  useEffect(() => {
    if (map.current || !mapContainer.current) return;

    const initialCenter = trackData.length > 0
      ? [trackData[0].lng, trackData[0].lat]
      : [118.0149, -2.5489];

    try {
      map.current = new maptilersdk.Map({
        container: mapContainer.current,
        style: maptilersdk.MapStyle.STREETS, // Using vector streets style
        center: initialCenter,
        zoom: 12,
      });

      map.current.on('load', () => {
        console.log('MapTiler replay map loaded successfully');

        // Add transportation layer
        if (map.current) {
          // Add custom transportation layer
          map.current.addSource('transportation', {
            type: 'vector',
            url: 'https://api.maptiler.com/tiles/transportation/tiles.json?key=' + maptilersdk.config.apiKey
          });

          // Add road layers with different styles
          map.current.addLayer({
            id: 'transportation-roads',
            type: 'line',
            source: 'transportation',
            'source-layer': 'transportation',
            filter: ['==', ['get', 'class'], 'primary'],
            paint: {
              'line-color': '#4A90E2',
              'line-width': 3,
              'line-opacity': 0.8
            }
          });

          // Add secondary roads
          map.current.addLayer({
            id: 'transportation-secondary',
            type: 'line',
            source: 'transportation',
            'source-layer': 'transportation',
            filter: ['==', ['get', 'class'], 'secondary'],
            paint: {
              'line-color': '#7ED321',
              'line-width': 2,
              'line-opacity': 0.6
            }
          });
        }

        initializeTrack();
      });

      map.current.on('error', (e) => {
        console.error('MapTiler replay error:', e);
      });

    } catch (error) {
      console.error('Error initializing MapTiler replay:', error);
    }

  }, []);

  const getRouteCoordinates = async (): Promise<number[][]> => {
    if (trackData.length < 2) return trackData.map(point => [point.lng, point.lat]);

    try {
      // Create waypoints from track data (use every few points to avoid too many API calls)
      const waypoints = trackData.filter((_, index) =>
        index === 0 ||
        index === trackData.length - 1 ||
        index % Math.max(1, Math.floor(trackData.length / 10)) === 0
      );

      // Build coordinates string for MapTiler Directions API
      const coordinatesString = waypoints
        .map(point => `${point.lng},${point.lat}`)
        .join(';');

      const response = await fetch(
        `https://api.maptiler.com/directions/driving/${coordinatesString}?key=${maptilersdk.config.apiKey}&geometries=geojson&overview=full`
      );

      if (!response.ok) {
        throw new Error(`Directions API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0];
        if (route.geometry && route.geometry.coordinates) {
          return route.geometry.coordinates;
        }
      }

      throw new Error('No route found');
    } catch (error) {
      console.error('Error fetching route:', error);
      // Return original coordinates as fallback
      return trackData.map(point => [point.lng, point.lat]);
    }
  };

  const initializeTrack = async () => {
    if (!map.current || trackData.length === 0) return;

    try {
      // Create route following roads using MapTiler Directions API
      const coordinates = await getRouteCoordinates();
      setRouteCoordinates(coordinates);

      map.current.addSource('track', {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: coordinates
          }
        }
      });

      map.current.addLayer({
        id: 'track-line',
        type: 'line',
        source: 'track',
        paint: {
          'line-color': '#00D4AA',
          'line-width': 4,
          'line-opacity': 0.8
        }
      });
    } catch (error) {
      console.error('Error creating route:', error);
      // Fallback to straight line if routing fails
      const coordinates = trackData.map(point => [point.lng, point.lat]);
      setRouteCoordinates(coordinates);

      map.current.addSource('track', {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: coordinates
          }
        }
      });

      map.current.addLayer({
        id: 'track-line',
        type: 'line',
        source: 'track',
        paint: {
          'line-color': '#00D4AA',
          'line-width': 4,
          'line-opacity': 0.8
        }
      });
    }

    // Add start marker
    if (startLocation || trackData[0]) {
      const startPoint = startLocation || trackData[0];
      startMarker.current = new maptilersdk.Marker({
        color: "#22C55E",
        scale: 1.2
      })
        .setLngLat([startPoint.lng, startPoint.lat])
        .setPopup(new maptilersdk.Popup().setHTML('<div>Start Point</div>'))
        .addTo(map.current);
    }

    // Add end marker
    if (endLocation || trackData[trackData.length - 1]) {
      const endPoint = endLocation || trackData[trackData.length - 1];
      endMarker.current = new maptilersdk.Marker({
        color: "#EF4444",
        scale: 1.2
      })
        .setLngLat([endPoint.lng, endPoint.lat])
        .setPopup(new maptilersdk.Popup().setHTML('<div>End Point</div>'))
        .addTo(map.current);
    }

    // Add current position marker at the start of the route
    const startCoordinate = coordinates.length > 0 ? coordinates[0] : [trackData[0].lng, trackData[0].lat];
    currentMarker.current = new maptilersdk.Marker({
      color: "#3B82F6",
      scale: 1.5
    })
      .setLngLat(startCoordinate)
      .addTo(map.current);

    // Fit bounds to show entire track
    const bounds = new maptilersdk.LngLatBounds();
    trackData.forEach(point => {
      bounds.extend([point.lng, point.lat]);
    });
    map.current.fitBounds(bounds, { padding: 50 });
  };

  const playAnimation = () => {
    if (!routeCoordinates.length || currentIndex >= routeCoordinates.length - 1) return;

    const animate = () => {
      if (currentIndex < routeCoordinates.length - 1 && isPlaying) {
        const nextIndex = currentIndex + 1;
        const coordinate = routeCoordinates[nextIndex];

        // Find closest track data point for additional info
        const closestTrackPoint = findClosestTrackPoint(coordinate);

        if (currentMarker.current) {
          currentMarker.current.setLngLat(coordinate);

          // Update popup with current info
          const popup = new maptilersdk.Popup()
            .setHTML(`
              <div class="p-2">
                <div class="font-semibold">Current Position</div>
                <div class="text-sm">Time: ${closestTrackPoint?.timestamp || 'N/A'}</div>
                <div class="text-sm">Speed: ${closestTrackPoint?.speed || 0} km/h</div>
                <div class="text-sm">Lat: ${coordinate[1].toFixed(6)}</div>
                <div class="text-sm">Lng: ${coordinate[0].toFixed(6)}</div>
              </div>
            `);
          currentMarker.current.setPopup(popup);
        }

        if (map.current) {
          map.current.flyTo({
            center: coordinate,
            zoom: 15,
            duration: 1000 / playbackSpeed
          });
        }

        setCurrentIndex(nextIndex);

        animationRef.current = setTimeout(() => {
          animate();
        }, 1000 / playbackSpeed);
      } else {
        setIsPlaying(false);
      }
    };

    animate();
  };

  const findClosestTrackPoint = (coordinate: number[]) => {
    if (!trackData.length) return null;

    let closestPoint = trackData[0];
    let minDistance = getDistance(coordinate, [closestPoint.lng, closestPoint.lat]);

    for (const point of trackData) {
      const distance = getDistance(coordinate, [point.lng, point.lat]);
      if (distance < minDistance) {
        minDistance = distance;
        closestPoint = point;
      }
    }

    return closestPoint;
  };

  const getDistance = (coord1: number[], coord2: number[]) => {
    const dx = coord1[0] - coord2[0];
    const dy = coord1[1] - coord2[1];
    return Math.sqrt(dx * dx + dy * dy);
  };

  const handlePlay = () => {
    if (isPlaying) {
      setIsPlaying(false);
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
    } else {
      setIsPlaying(true);
      playAnimation();
    }
  };

  const handleStop = () => {
    setIsPlaying(false);
    setCurrentIndex(0);
    if (animationRef.current) {
      clearTimeout(animationRef.current);
    }
    if (currentMarker.current && routeCoordinates.length > 0) {
      currentMarker.current.setLngLat(routeCoordinates[0]);
    }
  };

  const handleSkipBack = () => {
    const skipAmount = Math.max(1, Math.floor(routeCoordinates.length / 20)); // Skip 5% of route
    const newIndex = Math.max(0, currentIndex - skipAmount);
    setCurrentIndex(newIndex);
    if (currentMarker.current && routeCoordinates[newIndex]) {
      currentMarker.current.setLngLat(routeCoordinates[newIndex]);
    }
  };

  const handleSkipForward = () => {
    const skipAmount = Math.max(1, Math.floor(routeCoordinates.length / 20)); // Skip 5% of route
    const newIndex = Math.min(routeCoordinates.length - 1, currentIndex + skipAmount);
    setCurrentIndex(newIndex);
    if (currentMarker.current && routeCoordinates[newIndex]) {
      currentMarker.current.setLngLat(routeCoordinates[newIndex]);
    }
  };

  useEffect(() => {
    if (isPlaying) {
      playAnimation();
    }
    return () => {
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
    };
  }, [isPlaying, playbackSpeed]);

  return (
    <div className="bg-white rounded-lg border overflow-hidden">
      {/* Controls */}
      <div className="p-4 border-b bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSkipBack}
              disabled={currentIndex === 0}
            >
              <SkipBack className="w-4 h-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handlePlay}
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleStop}
            >
              <Square className="w-4 h-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleSkipForward}
              disabled={currentIndex >= (routeCoordinates.length || trackData.length) - 1}
            >
              <SkipForward className="w-4 h-4" />
            </Button>
          </div>

          <div className="flex items-center gap-4">
            <div className="text-sm text-gray-600">
              Progress: {currentIndex + 1} / {routeCoordinates.length || trackData.length}
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Speed:</span>
              <select
                value={playbackSpeed}
                onChange={(e) => setPlaybackSpeed(Number(e.target.value))}
                className="text-sm border rounded px-2 py-1"
              >
                <option value={0.5}>0.5x</option>
                <option value={1}>1x</option>
                <option value={2}>2x</option>
                <option value={4}>4x</option>
              </select>
            </div>
          </div>
        </div>

        {/* Progress bar */}
        <div className="mt-3">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${(routeCoordinates.length || trackData.length) > 0 ? ((currentIndex + 1) / (routeCoordinates.length || trackData.length)) * 100 : 0}%`
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Map */}
      <div style={{ height }}>
        <div ref={mapContainer} className="w-full h-full" />
      </div>
    </div>
  );
}
