import React, { useRef, useEffect, useState } from 'react';
import * as maptilersdk from '@maptiler/sdk';
import "@maptiler/sdk/dist/maptiler-sdk.css";
import { Button } from '../ui/button';
import { Play, Pause, Square, SkipBack, SkipForward } from 'lucide-react';

maptilersdk.config.apiKey = 'a7S8ykFBG9qI73UllxnL';

interface TrackPoint {
  lat: number;
  lng: number;
  timestamp: string;
  speed?: number;
}

interface MapTilerReplayProps {
  trackData: TrackPoint[];
  height?: string;
  startLocation?: { lat: number; lng: number; address?: string };
  endLocation?: { lat: number; lng: number; address?: string };
}

export default function MapTilerReplay({
  trackData,
  height = '500px',
  startLocation,
  endLocation,
}: MapTilerReplayProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<maptilersdk.Map | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const animationRef = useRef<number | null>(null);
  const currentMarker = useRef<maptilersdk.Marker | null>(null);
  const startMarker = useRef<maptilersdk.Marker | null>(null);
  const endMarker = useRef<maptilersdk.Marker | null>(null);

  useEffect(() => {
    if (map.current || !mapContainer.current) return;

    const initialCenter = trackData.length > 0 
      ? [trackData[0].lng, trackData[0].lat]
      : [118.0149, -2.5489];

    try {
      map.current = new maptilersdk.Map({
        container: mapContainer.current,
        style: maptilersdk.MapStyle.STREETS, // Using vector streets style
        center: initialCenter,
        zoom: 12,
      });

      map.current.on('load', () => {
        console.log('MapTiler replay map loaded successfully');
        
        // Add transportation layer
        if (map.current) {
          // Add custom transportation layer
          map.current.addSource('transportation', {
            type: 'vector',
            url: 'https://api.maptiler.com/tiles/transportation/tiles.json?key=' + maptilersdk.config.apiKey
          });

          // Add road layers with different styles
          map.current.addLayer({
            id: 'transportation-roads',
            type: 'line',
            source: 'transportation',
            'source-layer': 'transportation',
            filter: ['==', ['get', 'class'], 'primary'],
            paint: {
              'line-color': '#4A90E2',
              'line-width': 3,
              'line-opacity': 0.8
            }
          });

          // Add secondary roads
          map.current.addLayer({
            id: 'transportation-secondary',
            type: 'line',
            source: 'transportation',
            'source-layer': 'transportation',
            filter: ['==', ['get', 'class'], 'secondary'],
            paint: {
              'line-color': '#7ED321',
              'line-width': 2,
              'line-opacity': 0.6
            }
          });
        }

        initializeTrack();
      });

      map.current.on('error', (e) => {
        console.error('MapTiler replay error:', e);
      });

    } catch (error) {
      console.error('Error initializing MapTiler replay:', error);
    }

  }, []);

  const initializeTrack = () => {
    if (!map.current || trackData.length === 0) return;

    // Add track line
    const coordinates = trackData.map(point => [point.lng, point.lat]);
    
    map.current.addSource('track', {
      type: 'geojson',
      data: {
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'LineString',
          coordinates: coordinates
        }
      }
    });

    map.current.addLayer({
      id: 'track-line',
      type: 'line',
      source: 'track',
      paint: {
        'line-color': '#00D4AA',
        'line-width': 4,
        'line-opacity': 0.8
      }
    });

    // Add start marker
    if (startLocation || trackData[0]) {
      const startPoint = startLocation || trackData[0];
      startMarker.current = new maptilersdk.Marker({ 
        color: "#22C55E",
        scale: 1.2 
      })
        .setLngLat([startPoint.lng, startPoint.lat])
        .setPopup(new maptilersdk.Popup().setHTML('<div>Start Point</div>'))
        .addTo(map.current);
    }

    // Add end marker
    if (endLocation || trackData[trackData.length - 1]) {
      const endPoint = endLocation || trackData[trackData.length - 1];
      endMarker.current = new maptilersdk.Marker({ 
        color: "#EF4444",
        scale: 1.2 
      })
        .setLngLat([endPoint.lng, endPoint.lat])
        .setPopup(new maptilersdk.Popup().setHTML('<div>End Point</div>'))
        .addTo(map.current);
    }

    // Add current position marker
    currentMarker.current = new maptilersdk.Marker({ 
      color: "#3B82F6",
      scale: 1.5 
    })
      .setLngLat([trackData[0].lng, trackData[0].lat])
      .addTo(map.current);

    // Fit bounds to show entire track
    const bounds = new maptilersdk.LngLatBounds();
    trackData.forEach(point => {
      bounds.extend([point.lng, point.lat]);
    });
    map.current.fitBounds(bounds, { padding: 50 });
  };

  const playAnimation = () => {
    if (!trackData.length || currentIndex >= trackData.length - 1) return;

    const animate = () => {
      if (currentIndex < trackData.length - 1 && isPlaying) {
        const nextIndex = currentIndex + 1;
        const point = trackData[nextIndex];
        
        if (currentMarker.current) {
          currentMarker.current.setLngLat([point.lng, point.lat]);
          
          // Update popup with current info
          const popup = new maptilersdk.Popup()
            .setHTML(`
              <div class="p-2">
                <div class="font-semibold">Current Position</div>
                <div class="text-sm">Time: ${point.timestamp}</div>
                <div class="text-sm">Speed: ${point.speed || 0} km/h</div>
              </div>
            `);
          currentMarker.current.setPopup(popup);
        }

        if (map.current) {
          map.current.flyTo({
            center: [point.lng, point.lat],
            zoom: 15,
            duration: 1000 / playbackSpeed
          });
        }

        setCurrentIndex(nextIndex);
        
        animationRef.current = setTimeout(() => {
          animate();
        }, 1000 / playbackSpeed);
      } else {
        setIsPlaying(false);
      }
    };

    animate();
  };

  const handlePlay = () => {
    if (isPlaying) {
      setIsPlaying(false);
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
    } else {
      setIsPlaying(true);
      playAnimation();
    }
  };

  const handleStop = () => {
    setIsPlaying(false);
    setCurrentIndex(0);
    if (animationRef.current) {
      clearTimeout(animationRef.current);
    }
    if (currentMarker.current && trackData.length > 0) {
      currentMarker.current.setLngLat([trackData[0].lng, trackData[0].lat]);
    }
  };

  const handleSkipBack = () => {
    const newIndex = Math.max(0, currentIndex - 10);
    setCurrentIndex(newIndex);
    if (currentMarker.current && trackData[newIndex]) {
      currentMarker.current.setLngLat([trackData[newIndex].lng, trackData[newIndex].lat]);
    }
  };

  const handleSkipForward = () => {
    const newIndex = Math.min(trackData.length - 1, currentIndex + 10);
    setCurrentIndex(newIndex);
    if (currentMarker.current && trackData[newIndex]) {
      currentMarker.current.setLngLat([trackData[newIndex].lng, trackData[newIndex].lat]);
    }
  };

  useEffect(() => {
    if (isPlaying) {
      playAnimation();
    }
    return () => {
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
    };
  }, [isPlaying, playbackSpeed]);

  return (
    <div className="bg-white rounded-lg border overflow-hidden">
      {/* Controls */}
      <div className="p-4 border-b bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSkipBack}
              disabled={currentIndex === 0}
            >
              <SkipBack className="w-4 h-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handlePlay}
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleStop}
            >
              <Square className="w-4 h-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleSkipForward}
              disabled={currentIndex >= trackData.length - 1}
            >
              <SkipForward className="w-4 h-4" />
            </Button>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="text-sm text-gray-600">
              Progress: {currentIndex + 1} / {trackData.length}
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Speed:</span>
              <select
                value={playbackSpeed}
                onChange={(e) => setPlaybackSpeed(Number(e.target.value))}
                className="text-sm border rounded px-2 py-1"
              >
                <option value={0.5}>0.5x</option>
                <option value={1}>1x</option>
                <option value={2}>2x</option>
                <option value={4}>4x</option>
              </select>
            </div>
          </div>
        </div>
        
        {/* Progress bar */}
        <div className="mt-3">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${trackData.length > 0 ? ((currentIndex + 1) / trackData.length) * 100 : 0}%`
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Map */}
      <div style={{ height }}>
        <div ref={mapContainer} className="w-full h-full" />
      </div>
    </div>
  );
}
