import { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMap } from 'react-leaflet';
import { Icon, LatLngBounds } from 'leaflet';
import L from 'leaflet';

// Fix for default markers in React Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

// Create custom icons with different colors
const originIcon = new Icon({
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const destinationIcon = new Icon({
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

interface LocationData {
  lat: number;
  lng: number;
  address?: string;
}

interface MapWithRouteProps {
  originLocation?: LocationData;
  destinationLocation?: LocationData;
  onOriginSelect: (location: LocationData) => void;
  onDestinationSelect: (location: LocationData) => void;
  height?: string;
}

// Component to handle map clicks and auto-fit bounds
function MapController({ 
  originLocation, 
  destinationLocation, 
  onOriginSelect, 
  onDestinationSelect 
}: {
  originLocation?: LocationData;
  destinationLocation?: LocationData;
  onOriginSelect: (location: LocationData) => void;
  onDestinationSelect: (location: LocationData) => void;
}) {
  const map = useMap();

  // Auto-fit bounds when both locations are available
  useEffect(() => {
    if (originLocation && destinationLocation) {
      const bounds = new LatLngBounds(
        [originLocation.lat, originLocation.lng],
        [destinationLocation.lat, destinationLocation.lng]
      );
      map.fitBounds(bounds, { padding: [50, 50] });
    } else if (originLocation) {
      map.setView([originLocation.lat, originLocation.lng], 13);
    } else if (destinationLocation) {
      map.setView([destinationLocation.lat, destinationLocation.lng], 13);
    }
  }, [map, originLocation, destinationLocation]);

  // Handle map clicks
  useEffect(() => {
    const handleClick = async (e: any) => {
      const { lat, lng } = e.latlng;
      console.log('Map clicked:', { lat, lng });

      // Reverse geocoding to get address
      try {
        const response = await fetch(
          `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`
        );
        const data = await response.json();

        const location = {
          lat,
          lng,
          address: data.display_name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`
        };

        console.log('Location created:', location);

        // If no origin, set as origin. If origin exists but no destination, set as destination
        if (!originLocation) {
          console.log('Setting as origin');
          onOriginSelect(location);
        } else if (!destinationLocation) {
          console.log('Setting as destination');
          onDestinationSelect(location);
        } else {
          // Both exist, determine which is closer to click
          const distToOrigin = Math.sqrt(
            Math.pow(lat - originLocation.lat, 2) + Math.pow(lng - originLocation.lng, 2)
          );
          const distToDestination = Math.sqrt(
            Math.pow(lat - destinationLocation.lat, 2) + Math.pow(lng - destinationLocation.lng, 2)
          );

          if (distToOrigin < distToDestination) {
            console.log('Updating origin (closer)');
            onOriginSelect(location);
          } else {
            console.log('Updating destination (closer)');
            onDestinationSelect(location);
          }
        }
      } catch (error) {
        console.error('Error getting address:', error);
        const location = {
          lat,
          lng,
          address: `${lat.toFixed(6)}, ${lng.toFixed(6)}`
        };

        if (!originLocation) {
          console.log('Setting as origin (fallback)');
          onOriginSelect(location);
        } else {
          console.log('Setting as destination (fallback)');
          onDestinationSelect(location);
        }
      }
    };

    map.on('click', handleClick);

    return () => {
      map.off('click', handleClick);
    };
  }, [map, originLocation, destinationLocation, onOriginSelect, onDestinationSelect]);

  return null;
}

export function MapWithRoute({
  originLocation,
  destinationLocation,
  onOriginSelect,
  onDestinationSelect,
  height = '400px'
}: MapWithRouteProps) {
  // Default center to Jakarta, Indonesia
  const defaultCenter: [number, number] = [-6.2088, 106.8456];
  
  // Determine map center
  const mapCenter: [number, number] = 
    originLocation ? [originLocation.lat, originLocation.lng] :
    destinationLocation ? [destinationLocation.lat, destinationLocation.lng] :
    defaultCenter;

  // Create route line between origin and destination
  const routePositions: [number, number][] = [];
  if (originLocation && destinationLocation) {
    routePositions.push(
      [originLocation.lat, originLocation.lng],
      [destinationLocation.lat, destinationLocation.lng]
    );
  }

  return (
    <div style={{ height }} className="rounded-lg overflow-hidden border border-gray-300 relative">
      <MapContainer
        center={mapCenter}
        zoom={13}
        style={{ height: '100%', width: '100%' }}
        scrollWheelZoom={true}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        {/* Map Controller */}
        <MapController 
          originLocation={originLocation}
          destinationLocation={destinationLocation}
          onOriginSelect={onOriginSelect}
          onDestinationSelect={onDestinationSelect}
        />
        
        {/* Origin Marker */}
        {originLocation && (
          <Marker 
            position={[originLocation.lat, originLocation.lng]} 
            icon={originIcon}
          >
            <Popup>
              <div className="text-sm">
                <div className="font-semibold text-red-600 mb-1">📍 Lokasi Asal</div>
                <div className="mb-1">{originLocation.address}</div>
                <div className="text-xs text-gray-500">
                  {originLocation.lat.toFixed(6)}, {originLocation.lng.toFixed(6)}
                </div>
              </div>
            </Popup>
          </Marker>
        )}
        
        {/* Destination Marker */}
        {destinationLocation && (
          <Marker 
            position={[destinationLocation.lat, destinationLocation.lng]} 
            icon={destinationIcon}
          >
            <Popup>
              <div className="text-sm">
                <div className="font-semibold text-green-600 mb-1">🎯 Lokasi Tujuan</div>
                <div className="mb-1">{destinationLocation.address}</div>
                <div className="text-xs text-gray-500">
                  {destinationLocation.lat.toFixed(6)}, {destinationLocation.lng.toFixed(6)}
                </div>
              </div>
            </Popup>
          </Marker>
        )}
        
        {/* Route Line */}
        {routePositions.length === 2 && (
          <Polyline 
            positions={routePositions} 
            color="#3b82f6" 
            weight={3}
            opacity={0.7}
            dashArray="10, 10"
          />
        )}
      </MapContainer>
      
      {/* Legend */}
      <div className="absolute top-4 right-4 bg-white bg-opacity-90 p-3 rounded-lg shadow-lg text-xs">
        <div className="font-semibold mb-2">Legenda:</div>
        <div className="flex items-center mb-1">
          <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
          <span>Lokasi Asal</span>
        </div>
        <div className="flex items-center mb-1">
          <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span>Lokasi Tujuan</span>
        </div>
        <div className="flex items-center">
          <div className="w-6 h-0.5 bg-blue-500 mr-2" style={{borderTop: '2px dashed #3b82f6'}}></div>
          <span>Rute</span>
        </div>
      </div>
    </div>
  );
}
