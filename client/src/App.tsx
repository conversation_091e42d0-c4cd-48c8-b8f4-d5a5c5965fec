import { useEffect, useState, ReactNode } from "react";
import { Routes, Route, useNavigate, useLocation, Navigate, Outlet, useParams } from "react-router-dom";
import { Layout } from "./components/Layout";
import Login from "./pages/Login";
import Register from "./pages/Register";
import ForgotPassword from "./pages/ForgotPassword";
import DataESeal from "./pages/superadmin/DataESeal";
import TambahData from "./pages/superadmin/TambahData";
import DokumenKepabeansuperadmin from "./pages/superadmin/DokumenKepabean";
import StartStopSuperadmin from "./pages/superadmin/StartStop";
import StatusSuperadmin from "./pages/superadmin/Status";
import MonitoringSuperadmin from "./pages/superadmin/Monitoring";
import LaporanSuperadmin from "./pages/superadmin/Laporan";
import Logs from "./pages/superadmin/Logs/Logs";
import ManajemenOrganisasi from "./pages/superadmin/ManajemenOrganisasi";
import ManajemenPengguna from "./pages/superadmin/PengaturanSIstem/ManajemenPengguna";
import RoleManagement from "./pages/superadmin/PengaturanSIstem/RoleManagement";
import DataESealAdmin from "./pages/admin/[slug]/DataESeal";
import DokumenKepabean from "./pages/admin/[slug]/DokumenKepabean";
import StartStop from "./pages/admin/[slug]/StartStop";
import Status from "./pages/admin/[slug]/Status";
import Monitoring from "./pages/admin/[slug]/Monitoring";
import Laporan from "./pages/admin/[slug]/Laporan";
import LogsAdmin from "./pages/admin/[slug]/Logs";
import { useSession, useActiveOrganization } from "@/lib/auth-client";

// Protected Route Component
const ProtectedRoute = ({ children, allowedRoles }: { children: ReactNode; allowedRoles: string[] }) => {
  const { data: session, isPending } = useSession();

  if (isPending) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  if (!session?.user) {
    return <Navigate to="/login" replace />;
  }

  const userRole = session.user.role as string;

  if (!allowedRoles.includes(userRole)) {
    // If user's role is not allowed, redirect them
    // For example, redirect to login or a generic dashboard
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

const AdminLayout = ({ activeMenu, onMenuClick }: { activeMenu: string, onMenuClick: (menu: string) => void }) => {
  const { slug } = useParams<{ slug: string }>();
  const { data: activeOrganization, isPending } = useActiveOrganization();

  if (isPending) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  if (!slug || slug !== activeOrganization?.slug) {
    return <Navigate to="/login" replace />;
  }

  return (
    <Layout activeMenu={activeMenu} onMenuClick={onMenuClick}>
      <Outlet />
    </Layout>
  );
};

function App() {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeMenu, setActiveMenu] = useState("data-eseal");
  const { data: session, isPending } = useSession();
  const { data: activeOrganization, isPending: isOrgPending } = useActiveOrganization();
  const isAuthenticated = !!session;

  // Map paths to menu IDs
  const pathToMenuId = {
    "/": "tambah-data",
    "/superadmin/tambah-data": "tambah-data",
    "/superadmin/dokumen-kepabean": "dokumen-kepabean",
    "/superadmin/start-stop": "start-stop",
    "/superadmin/status": "status",
    "/superadmin/monitoring": "monitoring",
    "/superadmin/laporan": "laporan",
    "/superadmin/logs": "logs",
    "/superadmin/manajemen-organisasi": "manajemen-organisasi",
    "/superadmin/manajemen-pengguna": "manajemen-pengguna",
    "/superadmin/role-management": "role-management",
    "/admin/:slug/data-eseal": "data-eseal",
    "/admin/:slug/dokumen-kepabean": "dokumen-kepabean",
    "/admin/:slug/start-stop": "start-stop",
    "/admin/:slug/status": "status",
    "/admin/:slug/monitoring": "monitoring",
    "/admin/:slug/laporan": "laporan",
    "/admin/:slug/logs": "logs-admin",
  };

  // Update active menu based on current path
  useEffect(() => {
    const { pathname } = location;
    const matchedPath = Object.keys(pathToMenuId).find(path => {
      const pathSegments = path.split('/');
      const pathnameSegments = pathname.split('/');
      if (pathSegments.length !== pathnameSegments.length) {
        return false;
      }
      return pathSegments.every((segment, i) => {
        return segment.startsWith(':') || segment === pathnameSegments[i];
      });
    });

    const menuId = matchedPath ? pathToMenuId[matchedPath as keyof typeof pathToMenuId] : "tambah-data";
    setActiveMenu(menuId);
  }, [location.pathname]);

  const handleMenuClick = (menuId: string) => {
    const userRole = session?.user?.role;
    const orgSlug = activeOrganization?.slug;
    const menuToPath = {
      "tambah-data": "/superadmin/tambah-data",
      "data-eseal": userRole === 'admin' ? `/admin/${orgSlug}/data-eseal` : "/superadmin/tambah-data",
      "dokumen-kepabean": userRole === 'admin' ? `/admin/${orgSlug}/dokumen-kepabean` : "/superadmin/dokumen-kepabean",
      "start-stop": userRole === 'admin' ? `/admin/${orgSlug}/start-stop` : "/superadmin/start-stop",
      "status": userRole === 'admin' ? `/admin/${orgSlug}/status` : "/superadmin/status",
      "monitoring": userRole === 'admin' ? `/admin/${orgSlug}/monitoring` : "/superadmin/monitoring",
      "laporan": userRole === 'admin' ? `/admin/${orgSlug}/laporan` : "/superadmin/laporan",
      "logs-admin": `/admin/${orgSlug}/logs`,
      "logs": "/superadmin/logs",
      "manajemen-organisasi": "/superadmin/manajemen-organisasi",
      "manajemen-pengguna": "/superadmin/manajemen-pengguna",
      "role-management": "/superadmin/role-management",
    };

    const path = menuToPath[menuId as keyof typeof menuToPath] || (userRole === 'admin' ? `/admin/${orgSlug}/data-eseal` : '/superadmin/tambah-data');
    navigate(path);
  };

  // Redirect logic after login
  useEffect(() => {
    if (!isPending && !isOrgPending && isAuthenticated) {
      const userRole = session.user.role as string;
      const currentPath = location.pathname;

      if (userRole === 'superadmin' && !currentPath.startsWith('/superadmin')) {
        navigate('/superadmin/tambah-data');
      } else if (userRole === 'admin' && !currentPath.startsWith('/admin')) {
        const orgSlug = activeOrganization?.slug;
        if (orgSlug) {
          navigate(`/admin/${orgSlug}/data-eseal`);
        } else {
          // Handle case where admin has no organization
          // console.log("Admin has no active organization, redirecting to login.");
          // authClient.auth.signOut();
        }
      } else if (!userRole && currentPath !== '/login') {
        // If role is not defined, redirect to login
        navigate('/login');
      }
    }
  }, [isAuthenticated, isPending, isOrgPending, session, activeOrganization, location.pathname, navigate]);

  // If session is loading, show loading state
  if (isPending) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  return (
    <Routes>
      {/* Public routes */}
      <Route path="/login" element={<Login />} />
      <Route path="/register" element={<Register />} />
      <Route path="/forgot-password" element={<ForgotPassword />} />

      {/* Protected Admin Routes */}
      <Route
        path="/admin/:slug"
        element={
          <ProtectedRoute allowedRoles={['admin']}>
            <AdminLayout activeMenu={activeMenu} onMenuClick={handleMenuClick} />
          </ProtectedRoute>
        }
      >
        <Route path="data-eseal" element={<DataESealAdmin />} />
        <Route path="dokumen-kepabean" element={<DokumenKepabean />} />
        <Route path="start-stop" element={<StartStop />} />
        <Route path="status" element={<Status />} />
        <Route path="monitoring" element={<Monitoring />} />
        <Route path="laporan" element={<Laporan />} />
        <Route path="logs" element={<LogsAdmin />} />
        <Route index element={<Navigate to="data-eseal" />} />
        <Route path="*" element={<Navigate to="data-eseal" />} />
      </Route>

      {/* Protected SuperAdmin Routes */}
      <Route
        path="/superadmin/*"
        element={
          <ProtectedRoute allowedRoles={['superadmin']}>
            <Layout activeMenu={activeMenu} onMenuClick={handleMenuClick}>
              <Routes>
                <Route path="tambah-data" element={<TambahData />} />
                <Route path="dokumen-kepabean" element={<DokumenKepabeansuperadmin />} />
                <Route path="start-stop" element={<StartStopSuperadmin />} />
                <Route path="status" element={<StatusSuperadmin />} />
                <Route path="monitoring" element={<MonitoringSuperadmin />} />
                <Route path="laporan" element={<LaporanSuperadmin />} />
                <Route path="logs" element={<Logs />} />
                <Route path="manajemen-organisasi" element={<ManajemenOrganisasi />} />
                <Route path="manajemen-pengguna" element={<ManajemenPengguna />} />
                <Route path="role-management" element={<RoleManagement />} />
                <Route path="*" element={<Navigate to="tambah-data" />} />
              </Routes>
            </Layout>
          </ProtectedRoute>
        }
      />

      {/* Default route redirection */}
      <Route
        path="/"
        element={
          <ProtectedRoute allowedRoles={['superadmin', 'admin']}>
            <HomeRedirect />
          </ProtectedRoute>
        }
      />

      {/* Fallback for any other route */}
      <Route path="*" element={<Navigate to="/" />} />
    </Routes>
  );
}

// Component to handle redirection from root based on role
const HomeRedirect = () => {
  const { data: session, isPending: isSessionPending } = useSession();
  const { data: activeOrganization, isPending: isOrgPending } = useActiveOrganization();

  if (isSessionPending || isOrgPending) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  const userRole = session?.user?.role as string;
  const orgSlug = activeOrganization?.slug;

  if (userRole === 'superadmin') {
    return <Navigate to="/superadmin/tambah-data" replace />;
  }
  if (userRole === 'admin') {
    if (orgSlug) {
      return <Navigate to={`/admin/${orgSlug}/data-eseal`} replace />;
    }
    // Fallback for admin without organization, maybe sign out and redirect
    // authClient.auth.signOut();
    return <Navigate to="/login" replace />;
  }
  // Fallback to login if role is not found or not matching
  return <Navigate to="/login" replace />;
};

export default App;
