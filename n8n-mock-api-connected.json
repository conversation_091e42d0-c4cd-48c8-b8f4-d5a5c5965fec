{"name": "E-Seal Mock API (Connected)", "nodes": [{"parameters": {"path": "{{$json.path}}", "responseMode": "responseNode", "options": {}}, "id": "main-webhook", "name": "Main Mock API Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [0, 500]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "{{$json.path}}", "rightValue": "eseal/add", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "if-eseal-add", "name": "IF E-Seal Add", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [240, 200]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil menambahkan data ke tabel td_eseal\",\n  \"item\": {\n    \"idEseal\": \"mock-eseal-id\",\n    \"merk\": \"{{$json.body.merk}}\",\n    \"model\": \"{{$json.body.model}}\",\n    \"tipe\": \"{{$json.body.tipe}}\",\n    \"idVendor\": \"{{$json.body.idVendor}}\",\n    \"noImei\": \"{{$json.body.noImei}}\",\n    \"status\": \"1\",\n    \"wkRekam\": \"{{$now.toMillis()}}\",\n    \"wkUpdate\": \"{{$now.toMillis()}}\"\n  }\n}"}, "id": "eseal-add-response", "name": "E-Seal Add Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 200]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "{{$json.path}}", "rightValue": "eseal/get-dok-pabean", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "if-get-dok-pabean", "name": "IF Get Dok Pabean", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [240, 300]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil mendapatkan dokumen pabean\",\n  \"item\": {\n    \"nomorAju\": \"{{$json.query.nomor_aju}}\",\n    \"kodeDokumen\": \"20\",\n    \"nomorDaftar\": \"000123\",\n    \"tanggalDaftar\": \"2024-01-01\",\n    \"kodeKantor\": \"050100\",\n    \"namaKantor\": \"KPU Bea dan Cukai Tipe A Tanjung Priok\",\n    \"kodeTps\": \"KOJA\",\n    \"namaGudang\": \"Gudang KOJA\",\n    \"idPengusaha\": \"123456789012345\",\n    \"namaPengusaha\": \"PT. IMPORTIR SEJAHTERA\",\n    \"uraian\": \"BARANG IMPOR\",\n    \"kontainer\": [\n      {\n        \"nomorKontainer\": \"DRYU9876543\",\n        \"nomorSegel\": \"BC-123456\"\n      }\n    ]\n  }\n}"}, "id": "get-dok-pabean-response", "name": "Get Dok Pabean Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "{{$json.path}}", "rightValue": "tracking/start", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "if-tracking-start", "name": "IF Tracking Start", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [240, 400]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil memulai tracking\",\n  \"item\": {\n    \"trackingId\": \"track-{{$now.toMillis()}}\"\n  }\n}"}, "id": "tracking-start-response", "name": "Tracking Start Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 400]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "{{$json.path}}", "rightValue": "eseal/update-position", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "if-update-position", "name": "IF Update Position", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [240, 500]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil menerima data posisi\"\n}"}, "id": "update-position-response", "name": "Update Position Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 500]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "{{$json.path}}", "rightValue": "tracking/stop", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "if-tracking-stop", "name": "IF Tracking Stop", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [240, 600]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil menghentikan tracking\",\n  \"item\": {\n    \"trackingId\": \"track-{{$now.toMillis()}}\"\n  }\n}"}, "id": "tracking-stop-response", "name": "Tracking Stop Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 600]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "{{$json.path}}", "rightValue": "eseal/updatestatus-device", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "if-update-status-device", "name": "IF Update Status Device", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [240, 700]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil mengubah data di tabel td_eseal\"\n}"}, "id": "update-status-device-response", "name": "Update Status Device Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 700]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "{{$json.path}}", "rightValue": "tracking/status", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "if-tracking-status", "name": "IF Tracking Status", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [240, 800]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Berhasil mendapatkan data tracking untuk nomor eseal {{$json.query.noEseal}}\",\n  \"item\": {\n    \"start\": \"success\",\n    \"updatePosition\": 10,\n    \"stop\": \"pending\"\n  }\n}"}, "id": "tracking-status-response", "name": "Tracking Status Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 800]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "{{$json.path}}", "rightValue": "device/list_all", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "if-device-list-all", "name": "IF Device List All", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [240, 900]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\": 200,\n  \"data\": [\n    {\n      \"deviceId\": \"device-001\",\n      \"vehicleId\": 1,\n      \"vehicleName\": \"Truck A\",\n      \"online\": true,\n      \"lat\": -6.175392,\n      \"lng\": 106.827153,\n      \"speed\": 50,\n      \"devBatteryPCT\": 85,\n      \"lockStatus\": 1\n    },\n    {\n      \"deviceId\": \"device-002\",\n      \"vehicleId\": 2,\n      \"vehicleName\": \"Truck B\",\n      \"online\": false,\n      \"lat\": -6.208763,\n      \"lng\": 106.845599,\n      \"speed\": 0,\n      \"devBatteryPCT\": 50,\n      \"lockStatus\": 0\n    }\n  ],\n  \"message\": \"Success\"\n}"}, "id": "device-list-all-response", "name": "Device List All Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 900]}], "connections": {"main-webhook": {"main": [[{"node": "if-eseal-add", "type": "main", "index": 0}], [{"node": "if-get-dok-pabean", "type": "main", "index": 0}], [{"node": "if-tracking-start", "type": "main", "index": 0}], [{"node": "if-update-position", "type": "main", "index": 0}], [{"node": "if-tracking-stop", "type": "main", "index": 0}], [{"node": "if-update-status-device", "type": "main", "index": 0}], [{"node": "if-tracking-status", "type": "main", "index": 0}], [{"node": "if-device-list-all", "type": "main", "index": 0}]]}, "if-eseal-add": {"main": [[{"node": "eseal-add-response", "type": "main", "index": 0}]]}, "if-get-dok-pabean": {"main": [[{"node": "get-dok-pabean-response", "type": "main", "index": 0}]]}, "if-tracking-start": {"main": [[{"node": "tracking-start-response", "type": "main", "index": 0}]]}, "if-update-position": {"main": [[{"node": "update-position-response", "type": "main", "index": 0}]]}, "if-tracking-stop": {"main": [[{"node": "tracking-stop-response", "type": "main", "index": 0}]]}, "if-update-status-device": {"main": [[{"node": "update-status-device-response", "type": "main", "index": 0}]]}, "if-tracking-status": {"main": [[{"node": "tracking-status-response", "type": "main", "index": 0}]]}, "if-device-list-all": {"main": [[{"node": "device-list-all-response", "type": "main", "index": 0}]]}}, "pinData": {}}