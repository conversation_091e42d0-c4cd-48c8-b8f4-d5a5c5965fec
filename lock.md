{"swagger": "2.0", "info": {"description": "2.X", "version": "1.0", "title": "2.X", "termsOfService": "http://localhost:8080/doc.html"}, "host": "smartelock.net:8088", "basePath": "/", "tags": [{"name": "Alarm management", "x-order": "2147483647"}, {"name": "Command Management", "x-order": "4"}, {"name": "equipment management", "x-order": "2"}, {"name": "api_geofence_management", "x-order": "2147483647"}, {"name": "Report Management", "x-order": "2147483647"}, {"name": "api_time_management", "x-order": "2147483647"}, {"name": "User Management", "x-order": "1"}], "paths": {"/alarm/last_alarm_message": {"post": {"tags": ["Alarm management"], "summary": "Obtain device alarms", "operationId": "lastAlarmsUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "alarmMessageParamDto", "description": "alarmMessageParamDto", "required": true, "schema": {"$ref": "#/definitions/EmsAlarmMessageParamDto", "originalRef": "EmsAlarmMessageParamDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/alarm/list_aram_type": {"post": {"tags": ["Alarm management"], "summary": "Obtain alarm type", "operationId": "listAramTypeUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/alarm/list_car_alarm": {"post": {"tags": ["Alarm management"], "summary": "Obtain device alarms", "operationId": "listCarTrackUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "emsCarTrack", "description": "emsCarTrack", "required": true, "schema": {"$ref": "#/definitions/EmsTrackParamDto", "originalRef": "EmsTrackParamDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/command/more": {"post": {"tags": ["Command Management"], "summary": "Get Historical Instructions", "operationId": "moreCommandUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "deviceId", "in": "query", "description": "device ID", "required": true, "type": "string"}, {"name": "lastTime", "in": "query", "description": "The last time point, if left blank, defaults to 0, and only the latest pageSize data can be obtained", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "How many pieces of data per page, default to 10 pieces", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "6"}}, "/command/password_unlock": {"post": {"tags": ["Command Management"], "summary": "Password unlocking", "operationId": "passwordUnlockUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "passwordUnlockDto", "description": "passwordUnlockDto", "required": true, "schema": {"$ref": "#/definitions/EmsPasswordUnlockDto", "originalRef": "EmsPasswordUnlockDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«int»", "originalRef": "CommonResult«int»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«int»", "originalRef": "CommonResult«int»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2"}}, "/command/remote": {"post": {"tags": ["Command Management"], "summary": "Remote command operation", "operationId": "remoteCommandUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "commandDto", "description": "commandDto", "required": true, "schema": {"$ref": "#/definitions/EmsRemoteCommandDto", "originalRef": "EmsRemoteCommandDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«int»", "originalRef": "CommonResult«int»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«int»", "originalRef": "CommonResult«int»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "1"}}, "/command/remote_result": {"post": {"tags": ["Command Management"], "summary": "Remote operation&password unlocking result confirmation", "operationId": "remoteResultUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "remoteResultDto", "description": "remoteResultDto", "required": true, "schema": {"$ref": "#/definitions/EmsRemoteResultDto", "originalRef": "EmsRemoteResultDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«int»", "originalRef": "CommonResult«int»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«int»", "originalRef": "CommonResult«int»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "3"}}, "/command/send": {"post": {"tags": ["Command Management"], "summary": "Input instructions", "operationId": "sendUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "sendCommandDto", "description": "sendCommandDto", "required": true, "schema": {"$ref": "#/definitions/EmsSendCommandDto", "originalRef": "EmsSendCommandDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«EmsSendCommandResultDto»", "originalRef": "CommonResult«EmsSendCommandResultDto»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«EmsSendCommandResultDto»", "originalRef": "CommonResult«EmsSendCommandResultDto»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "4"}}, "/command/send_result": {"post": {"tags": ["Command Management"], "summary": "Obtain the latest instruction based on Id", "operationId": "sendResultUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "sendCommandResultDto", "description": "sendCommandResultDto", "required": true, "schema": {"$ref": "#/definitions/EmsSendCommandResultDto", "originalRef": "EmsSendCommandResultDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "5"}}, "/device/bind": {"post": {"tags": ["equipment management"], "summary": "Vehicle binding equipment", "operationId": "bindUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "bindDeviceDto", "description": "bindDeviceDto", "required": true, "schema": {"$ref": "#/definitions/EmsBindDeviceDto", "originalRef": "EmsBindDeviceDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/device/device_add": {"post": {"tags": ["equipment management"], "summary": "api_add_device", "operationId": "addDeviceUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "deviceTypeParamDto", "description": "deviceTypeParamDto", "required": true, "schema": {"$ref": "#/definitions/EmsDeviceTypeParamDto", "originalRef": "EmsDeviceTypeParamDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/device/device_types": {"post": {"tags": ["equipment management"], "summary": "api_get_device_types", "operationId": "deviceTypesUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/device/list_all": {"post": {"tags": ["equipment management"], "summary": "Obtain all devices of the user", "operationId": "listAllUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "offsetTime", "description": "offsetTime", "required": true, "schema": {"$ref": "#/definitions/OffsetTime", "originalRef": "OffsetTime"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«EmsSimpleDeviceDto»»", "originalRef": "CommonResult«List«EmsSimpleDeviceDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«EmsSimpleDeviceDto»»", "originalRef": "CommonResult«List«EmsSimpleDeviceDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/device/list_car_new_position": {"post": {"tags": ["equipment management"], "summary": "Get the latest location of the device", "operationId": "vehicleNewPointsUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "newPointsParamDto", "description": "newPointsParamDto", "required": true, "schema": {"$ref": "#/definitions/EmsTrackNewPointsParamDto", "originalRef": "EmsTrackNewPointsParamDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«EmsTrackDto»»", "originalRef": "CommonResult«List«EmsTrackDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«EmsTrackDto»»", "originalRef": "CommonResult«List«EmsTrackDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/device/list_car_track": {"post": {"tags": ["equipment management"], "summary": "Obtain device history trajectory", "operationId": "listCarTrackUsingPOST_3", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "emsCarTrack", "description": "emsCarTrack", "required": true, "schema": {"$ref": "#/definitions/EmsCarTrackDto", "originalRef": "EmsCarTrackDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/device/list_car_track_token": {"post": {"tags": ["equipment management"], "summary": "Obtain device history trajectory", "operationId": "listCarTrackTokenUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "emsCarTrack", "description": "emsCarTrack", "required": true, "schema": {"$ref": "#/definitions/EmsCarTrackTokenDto", "originalRef": "EmsCarTrackTokenDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/device/un_bind": {"post": {"tags": ["equipment management"], "summary": "Vehicle unbinding equipment", "operationId": "unBindUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "bindDeviceDto", "description": "bindDeviceDto", "required": true, "schema": {"$ref": "#/definitions/EmsBindDeviceDto", "originalRef": "EmsBindDeviceDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/device/vehicle_status": {"post": {"tags": ["equipment management"], "summary": "Obtain device status prompt", "operationId": "vehicleStatusUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/device/vehicle_status_icons": {"post": {"tags": ["equipment management"], "summary": "Obtain device status prompt icon", "operationId": "vehicleStatusIconsUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/geofence/add": {"post": {"tags": ["api_geofence_management"], "summary": "api_add_geofence", "operationId": "addUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "geofenceDto", "description": "geofenceDto", "required": true, "schema": {"$ref": "#/definitions/EmsGeofenceDto", "originalRef": "EmsGeofenceDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«EmsGeofence对象»", "originalRef": "CommonResult«EmsGeofence对象»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«EmsGeofence对象»", "originalRef": "CommonResult«EmsGeofence对象»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/geofence/add_and_join_car": {"post": {"tags": ["api_geofence_management"], "summary": "api_add_geofence", "operationId": "addAndJoinCarUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "geofenceDto", "description": "geofenceDto", "required": true, "schema": {"$ref": "#/definitions/EmsGeofenceDto", "originalRef": "EmsGeofenceDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«EmsGeofence对象»", "originalRef": "CommonResult«EmsGeofence对象»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«EmsGeofence对象»", "originalRef": "CommonResult«EmsGeofence对象»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/geofence/delete": {"post": {"tags": ["api_geofence_management"], "summary": "api_del_geofence", "operationId": "deleteUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "geofenceDto", "description": "geofenceDto", "required": true, "schema": {"$ref": "#/definitions/EmsGeofenceDto", "originalRef": "EmsGeofenceDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/geofence/delete_and_delete_car": {"post": {"tags": ["api_geofence_management"], "summary": "api_delete_geofence", "operationId": "deleteAndDeleteCarUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "geofenceDto", "description": "geofenceDto", "required": true, "schema": {"$ref": "#/definitions/EmsGeofenceDto", "originalRef": "EmsGeofenceDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/geofence/list_and_car": {"post": {"tags": ["api_geofence_management"], "summary": "api_get_list_for_car", "operationId": "listAndCarUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "geofenceDto", "description": "geofenceDto", "required": true, "schema": {"$ref": "#/definitions/EmsGeofenceDto", "originalRef": "EmsGeofenceDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«EmsGeofence对象»»", "originalRef": "CommonResult«List«EmsGeofence对象»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«EmsGeofence对象»»", "originalRef": "CommonResult«List«EmsGeofence对象»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/geofence/update": {"post": {"tags": ["api_geofence_management"], "summary": "api_update_geofence", "operationId": "updateUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "geofenceDto", "description": "geofenceDto", "required": true, "schema": {"$ref": "#/definitions/EmsGeofenceDto", "originalRef": "EmsGeofenceDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«EmsGeofence对象»", "originalRef": "CommonResult«EmsGeofence对象»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«EmsGeofence对象»", "originalRef": "CommonResult«EmsGeofence对象»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/report/alarm_report": {"post": {"tags": ["Report Management"], "summary": "Alarm Report", "operationId": "alarmReportUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "alarmReportDto", "description": "alarmReportDto", "required": true, "schema": {"$ref": "#/definitions/AlarmReportDto", "originalRef": "AlarmReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultAlarmReportDto»»", "originalRef": "CommonResult«List«ResultAlarmReportDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultAlarmReportDto»»", "originalRef": "CommonResult«List«ResultAlarmReportDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "11"}}, "/report/daily_mileage_report": {"post": {"tags": ["Report Management"], "summary": "Daily mileage report", "operationId": "dailyMileageUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingReportDto", "originalRef": "DrivingReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultGroupDrivingDailyMileagesDto«drivingReport对象»»»", "originalRef": "CommonResult«List«ResultGroupDrivingDailyMileagesDto«drivingReport对象»»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultGroupDrivingDailyMileagesDto«drivingReport对象»»»", "originalRef": "CommonResult«List«ResultGroupDrivingDailyMileagesDto«drivingReport对象»»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "5"}}, "/report/driving_behavior_report": {"post": {"tags": ["Report Management"], "summary": "Driving behavior report", "operationId": "drivingBehaviorReportUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingReportDto", "originalRef": "DrivingReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultGroupDrivingBehaviorDto»»", "originalRef": "CommonResult«List«ResultGroupDrivingBehaviorDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultGroupDrivingBehaviorDto»»", "originalRef": "CommonResult«List«ResultGroupDrivingBehaviorDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "11"}}, "/report/driving_parking_report": {"post": {"tags": ["Report Management"], "summary": "Parking report", "operationId": "drivingParkingReportUsingPOST_3", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingReportDto", "originalRef": "DrivingReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultGroupParkingReportDto»»", "originalRef": "CommonResult«List«ResultGroupParkingReportDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultGroupParkingReportDto»»", "originalRef": "CommonResult«List«ResultGroupParkingReportDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "11"}}, "/report/driving_summary_report": {"post": {"tags": ["Report Management"], "summary": "Driving Summary Report", "operationId": "drivingSummaryReportUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingReportDto", "originalRef": "DrivingReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultGroupDrivingSummaryDto«DrivingSummaryResultDto»»»", "originalRef": "CommonResult«List«ResultGroupDrivingSummaryDto«DrivingSummaryResultDto»»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultGroupDrivingSummaryDto«DrivingSummaryResultDto»»»", "originalRef": "CommonResult«List«ResultGroupDrivingSummaryDto«DrivingSummaryResultDto»»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "7"}}, "/report/idling_report": {"post": {"tags": ["Report Management"], "summary": "Idling Report", "operationId": "idleSpeedReportUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingReportDto", "originalRef": "DrivingReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultGroupParkingReportDto»»", "originalRef": "CommonResult«List«ResultGroupParkingReportDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultGroupParkingReportDto»»", "originalRef": "CommonResult«List«ResultGroupParkingReportDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "10"}}, "/report/last_alarms": {"post": {"tags": ["Report Management"], "summary": "Obtain alarm information based on index", "operationId": "getLastAlarmsUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "rmsAlarmParamDto", "description": "rmsAlarmParamDto", "required": true, "schema": {"$ref": "#/definitions/RmsAlarmParamDto", "originalRef": "RmsAlarmParamDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«ResultAlarmListDto»", "originalRef": "CommonResult«ResultAlarmListDto»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«ResultAlarmListDto»", "originalRef": "CommonResult«ResultAlarmListDto»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "5"}}, "/report/page/driving_day_mileage_report": {"post": {"tags": ["Report Management"], "summary": "Parking report", "operationId": "dayMileageReportUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingPageReportDto", "originalRef": "DrivingPageReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«drivingReport对象»»", "originalRef": "CommonResult«CommonPage«drivingReport对象»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«drivingReport对象»»", "originalRef": "CommonResult«CommonPage«drivingReport对象»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "11"}}, "/report/page/driving_history_track_report": {"post": {"tags": ["Report Management"], "summary": "Parking report", "operationId": "historyTrackReportUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingPageReportDto", "originalRef": "DrivingPageReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«EmsMongoTrackModel»»", "originalRef": "CommonResult«CommonPage«EmsMongoTrackModel»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«EmsMongoTrackModel»»", "originalRef": "CommonResult«CommonPage«EmsMongoTrackModel»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "11"}}, "/report/page/driving_iding_report": {"post": {"tags": ["Report Management"], "summary": "Parking report", "operationId": "drivingIdingReportUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingPageReportDto", "originalRef": "DrivingPageReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«drivingReport对象»»_1", "originalRef": "CommonResult«CommonPage«drivingReport对象»»_1"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«drivingReport对象»»_1", "originalRef": "CommonResult«CommonPage«drivingReport对象»»_1"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "11"}}, "/report/page/driving_in_out_geo_report": {"post": {"tags": ["Report Management"], "summary": "driving_in_out_geo_report", "operationId": "drivingEnterAndExitGeoReportUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingPageReportDto", "originalRef": "DrivingPageReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«车辆进出围栏报表»»", "originalRef": "CommonResult«CommonPage«车辆进出围栏报表»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«车辆进出围栏报表»»", "originalRef": "CommonResult«CommonPage«车辆进出围栏报表»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "11"}}, "/report/page/driving_over_speed_report": {"post": {"tags": ["Report Management"], "summary": "Parking report", "operationId": "overSpeedReportUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingPageReportDto", "originalRef": "DrivingPageReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«驾驶行为习惯对象»»", "originalRef": "CommonResult«CommonPage«驾驶行为习惯对象»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«驾驶行为习惯对象»»", "originalRef": "CommonResult«CommonPage«驾驶行为习惯对象»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "11"}}, "/report/page/driving_parking_report": {"post": {"tags": ["Report Management"], "summary": "Parking report", "operationId": "drivingParkingReportUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingPageReportDto", "originalRef": "DrivingPageReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«drivingReport对象»»_1", "originalRef": "CommonResult«CommonPage«drivingReport对象»»_1"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«drivingReport对象»»_1", "originalRef": "CommonResult«CommonPage«drivingReport对象»»_1"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "11"}}, "/report/page/driving_trip_report": {"post": {"tags": ["Report Management"], "summary": "Parking report", "operationId": "drivingTripReportUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingPageReportDto", "originalRef": "DrivingPageReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«drivingReport对象»»_1", "originalRef": "CommonResult«CommonPage«drivingReport对象»»_1"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«CommonPage«drivingReport对象»»_1", "originalRef": "CommonResult«CommonPage«drivingReport对象»»_1"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "11"}}, "/report/trip_report": {"post": {"tags": ["Report Management"], "summary": "Trip Report", "operationId": "runReportUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "drivingReportDto", "description": "drivingReportDto", "required": true, "schema": {"$ref": "#/definitions/DrivingReportDto", "originalRef": "DrivingReportDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultGroupRunReportDto»»", "originalRef": "CommonResult«List«ResultGroupRunReportDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult«List«ResultGroupRunReportDto»»", "originalRef": "CommonResult«List«ResultGroupRunReportDto»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "8"}}, "/time/update_day_mileage": {"post": {"tags": ["api_time_management"], "summary": "updateDayMileage", "operationId": "updateDayMileageUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "dataTime", "description": "dataTime", "required": true, "schema": {"$ref": "#/definitions/DataTime", "originalRef": "DataTime"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "5"}}, "/user/forgot_password": {"post": {"tags": ["User Management"], "summary": "api_forgot_password", "operationId": "forgotPasswordUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "emsForgotPasswordDto", "description": "emsForgotPasswordDto", "required": true, "schema": {"$ref": "#/definitions/EmsForgotPasswordDto", "originalRef": "EmsForgotPasswordDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/user/log_out": {"post": {"tags": ["User Management"], "summary": "api_reset_password", "operationId": "logOutUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/user/login": {"post": {"tags": ["User Management"], "summary": "log in", "operationId": "loginUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "userLoginParam", "description": "userLoginParam", "required": true, "schema": {"$ref": "#/definitions/UmsUserLoginDto", "originalRef": "UmsUserLoginDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/user/post_jiguang_id": {"post": {"tags": ["User Management"], "summary": "Save <PERSON> Push Id", "operationId": "postJiguangIdUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "jgDto", "description": "jgDto", "required": true, "schema": {"$ref": "#/definitions/UmsJgDto", "originalRef": "UmsJgDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/user/reset_password": {"post": {"tags": ["User Management"], "summary": "api_reset_password", "operationId": "resetPasswordUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "emsForgotPasswordDto", "description": "emsForgotPasswordDto", "required": true, "schema": {"$ref": "#/definitions/EmsForgotPasswordDto", "originalRef": "EmsForgotPasswordDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/user/send_email": {"post": {"tags": ["User Management"], "summary": "api_send_email", "operationId": "sendEmailUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "emsForgotPasswordDto", "description": "emsForgotPasswordDto", "required": true, "schema": {"$ref": "#/definitions/EmsForgotPasswordDto", "originalRef": "EmsForgotPasswordDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}, "/user/update_password": {"post": {"tags": ["User Management"], "summary": "Change password", "operationId": "updatePasswordUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "userPasswordDto", "description": "userPasswordDto", "required": true, "schema": {"$ref": "#/definitions/UmsUserPasswordDto", "originalRef": "UmsUserPasswordDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "responsesObject": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CommonResult", "originalRef": "CommonResult"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false, "x-order": "2147483647"}}}, "definitions": {"AlarmListDto": {"type": "object", "properties": {"alarmTypeLangKey": {"type": "string", "refType": null}, "alarmTypeText": {"type": "string", "refType": null}, "almTime": {"type": "integer", "format": "int32", "refType": null}, "almTimeStr": {"type": "string", "refType": null}, "almType": {"type": "integer", "format": "int32", "refType": null}, "autoIdx": {"type": "integer", "format": "int64", "refType": null}, "carId": {"type": "integer", "format": "int32", "refType": null}, "carName": {"type": "string", "refType": null}, "deviceId": {"type": "string", "refType": null}, "deviceType": {"type": "string", "refType": null}, "extData": {"type": "string", "refType": null}, "origin": {"type": "integer", "format": "int32", "refType": null}, "positionDataDto": {"$ref": "#/definitions/PositionDataDto", "originalRef": "PositionDataDto", "refType": "PositionDataDto"}}, "title": "AlarmListDto"}, "AlarmReportDto": {"type": "object", "properties": {"alarmType": {"type": "array", "items": {"type": "string"}, "refType": "string"}, "carIds": {"type": "array", "items": {"type": "string"}, "refType": "string"}, "endTime": {"type": "string", "description": "End Time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "startTime": {"type": "string", "description": "Start time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "timeOffset": {"type": "integer", "format": "int32", "description": "Standard time difference (seconds)", "refType": null}}, "title": "AlarmReportDto"}, "CommonPage«EmsMongoTrackModel»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/EmsMongoTrackModel", "originalRef": "EmsMongoTrackModel"}, "refType": "EmsMongoTrackModel"}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "pageSize": {"type": "integer", "format": "int32", "refType": null}, "total": {"type": "integer", "format": "int64", "refType": null}, "totalPage": {"type": "integer", "format": "int32", "refType": null}}, "title": "CommonPage«EmsMongoTrackModel»"}, "CommonPage«drivingReport对象»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/drivingReport对象", "originalRef": "drivingReport对象"}, "refType": "drivingReport对象"}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "pageSize": {"type": "integer", "format": "int32", "refType": null}, "total": {"type": "integer", "format": "int64", "refType": null}, "totalPage": {"type": "integer", "format": "int32", "refType": null}}, "title": "CommonPage«drivingReport对象»"}, "CommonPage«drivingReport对象»_1": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/drivingReport对象_1", "originalRef": "drivingReport对象_1"}, "refType": "drivingReport对象_1"}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "pageSize": {"type": "integer", "format": "int32", "refType": null}, "total": {"type": "integer", "format": "int64", "refType": null}, "totalPage": {"type": "integer", "format": "int32", "refType": null}}, "title": "CommonPage«drivingReport对象»_1"}, "CommonPage«车辆进出围栏报表»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/车辆进出围栏报表", "originalRef": "车辆进出围栏报表"}, "refType": "车辆进出围栏报表"}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "pageSize": {"type": "integer", "format": "int32", "refType": null}, "total": {"type": "integer", "format": "int64", "refType": null}, "totalPage": {"type": "integer", "format": "int32", "refType": null}}, "title": "CommonPage«车辆进出围栏报表»"}, "CommonPage«驾驶行为习惯对象»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/驾驶行为习惯对象", "originalRef": "驾驶行为习惯对象"}, "refType": "驾驶行为习惯对象"}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "pageSize": {"type": "integer", "format": "int32", "refType": null}, "total": {"type": "integer", "format": "int64", "refType": null}, "totalPage": {"type": "integer", "format": "int32", "refType": null}}, "title": "CommonPage«驾驶行为习惯对象»"}, "CommonResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "CommonResult"}, "CommonResult«CommonPage«EmsMongoTrackModel»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"$ref": "#/definitions/CommonPage«EmsMongoTrackModel»", "originalRef": "CommonPage«EmsMongoTrackModel»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«EmsMongoTrackModel»»"}, "CommonResult«CommonPage«drivingReport对象»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"$ref": "#/definitions/CommonPage«drivingReport对象»", "originalRef": "CommonPage«drivingReport对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«drivingReport对象»»"}, "CommonResult«CommonPage«drivingReport对象»»_1": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"$ref": "#/definitions/CommonPage«drivingReport对象»_1", "originalRef": "CommonPage«drivingReport对象»_1"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«drivingReport对象»»_1"}, "CommonResult«CommonPage«车辆进出围栏报表»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"$ref": "#/definitions/CommonPage«车辆进出围栏报表»", "originalRef": "CommonPage«车辆进出围栏报表»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«车辆进出围栏报表»»"}, "CommonResult«CommonPage«驾驶行为习惯对象»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"$ref": "#/definitions/CommonPage«驾驶行为习惯对象»", "originalRef": "CommonPage«驾驶行为习惯对象»"}, "message": {"type": "string"}}, "title": "CommonResult«CommonPage«驾驶行为习惯对象»»"}, "CommonResult«EmsGeofence对象»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"$ref": "#/definitions/EmsGeofence对象", "originalRef": "EmsGeofence对象"}, "message": {"type": "string"}}, "title": "CommonResult«EmsGeofence对象»"}, "CommonResult«EmsSendCommandResultDto»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"$ref": "#/definitions/EmsSendCommandResultDto", "originalRef": "EmsSendCommandResultDto"}, "message": {"type": "string"}}, "title": "CommonResult«EmsSendCommandResultDto»"}, "CommonResult«List«EmsGeofence对象»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/EmsGeofence对象", "originalRef": "EmsGeofence对象"}}, "message": {"type": "string"}}, "title": "CommonResult«List«EmsGeofence对象»»"}, "CommonResult«List«EmsSimpleDeviceDto»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/EmsSimpleDeviceDto", "originalRef": "EmsSimpleDeviceDto"}}, "message": {"type": "string"}}, "title": "CommonResult«List«EmsSimpleDeviceDto»»"}, "CommonResult«List«EmsTrackDto»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/EmsTrackDto", "originalRef": "EmsTrackDto"}}, "message": {"type": "string"}}, "title": "CommonResult«List«EmsTrackDto»»"}, "CommonResult«List«ResultAlarmReportDto»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ResultAlarmReportDto", "originalRef": "ResultAlarmReportDto"}}, "message": {"type": "string"}}, "title": "CommonResult«List«ResultAlarmReportDto»»"}, "CommonResult«List«ResultGroupDrivingBehaviorDto»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ResultGroupDrivingBehaviorDto", "originalRef": "ResultGroupDrivingBehaviorDto"}}, "message": {"type": "string"}}, "title": "CommonResult«List«ResultGroupDrivingBehaviorDto»»"}, "CommonResult«List«ResultGroupDrivingDailyMileagesDto«drivingReport对象»»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ResultGroupDrivingDailyMileagesDto«drivingReport对象»", "originalRef": "ResultGroupDrivingDailyMileagesDto«drivingReport对象»"}}, "message": {"type": "string"}}, "title": "CommonResult«List«ResultGroupDrivingDailyMileagesDto«drivingReport对象»»»"}, "CommonResult«List«ResultGroupDrivingSummaryDto«DrivingSummaryResultDto»»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ResultGroupDrivingSummaryDto«DrivingSummaryResultDto»", "originalRef": "ResultGroupDrivingSummaryDto«DrivingSummaryResultDto»"}}, "message": {"type": "string"}}, "title": "CommonResult«List«ResultGroupDrivingSummaryDto«DrivingSummaryResultDto»»»"}, "CommonResult«List«ResultGroupParkingReportDto»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ResultGroupParkingReportDto", "originalRef": "ResultGroupParkingReportDto"}}, "message": {"type": "string"}}, "title": "CommonResult«List«ResultGroupParkingReportDto»»"}, "CommonResult«List«ResultGroupRunReportDto»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ResultGroupRunReportDto", "originalRef": "ResultGroupRunReportDto"}}, "message": {"type": "string"}}, "title": "CommonResult«List«ResultGroupRunReportDto»»"}, "CommonResult«ResultAlarmListDto»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"$ref": "#/definitions/ResultAlarmListDto", "originalRef": "ResultAlarmListDto"}, "message": {"type": "string"}}, "title": "CommonResult«ResultAlarmListDto»"}, "CommonResult«int»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}}, "title": "CommonResult«int»"}, "CorneringOverSpeedDto": {"type": "object", "properties": {"alarmType": {"type": "string", "description": "Alarm type", "refType": null}, "carName": {"type": "string", "description": "carName", "refType": null}, "dateTime": {"type": "string", "description": "Time", "refType": null}, "lat": {"type": "number", "format": "double", "description": "latitude", "refType": null}, "lng": {"type": "number", "format": "double", "description": "longitude", "refType": null}, "rfidNum": {"type": "string", "description": "driverRFID", "refType": null}}, "title": "CorneringOverSpeedDto"}, "DataTime": {"type": "object", "properties": {"endTime": {"type": "string", "description": "End Time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "startTime": {"type": "string", "description": "Start time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "timeOffset": {"type": "integer", "format": "int32", "description": "Standard time difference (seconds)", "refType": null}}, "title": "DataTime"}, "DrivingCollisionDto": {"type": "object", "properties": {"alarmType": {"type": "string", "description": "Alarm type", "refType": null}, "carName": {"type": "string", "description": "carName", "refType": null}, "dateTime": {"type": "string", "description": "Time", "refType": null}, "lat": {"type": "number", "format": "double", "description": "latitude", "refType": null}, "lng": {"type": "number", "format": "double", "description": "longitude", "refType": null}, "rfidNum": {"type": "string", "description": "driverRFID", "refType": null}}, "title": "DrivingCollisionDto"}, "DrivingPageReportDto": {"type": "object", "properties": {"carId": {"type": "integer", "format": "int32", "refType": null}, "endTime": {"type": "string", "description": "End Time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "pageSize": {"type": "integer", "format": "int32", "refType": null}, "startTime": {"type": "string", "description": "Start time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "timeOffset": {"type": "integer", "format": "int32", "description": "Standard time difference (seconds)", "refType": null}}, "title": "DrivingPageReportDto"}, "DrivingReportDto": {"type": "object", "required": ["data", "type"], "properties": {"data": {"type": "array", "description": "If the request parameter type=='carId', the carId set will be passed; If type=='RFID', transfer the RFID set ", "items": {"type": "string"}, "refType": "string"}, "endTime": {"type": "string", "description": "End Time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "pageSize": {"type": "integer", "format": "int32", "refType": null}, "startTime": {"type": "string", "description": "Start time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "timeOffset": {"type": "integer", "format": "int32", "description": "Standard time difference (seconds)", "refType": null}, "type": {"type": "string", "description": "carId|rfid ", "refType": null}}, "title": "DrivingReportDto"}, "DrivingSummaryResultDto": {"type": "object", "properties": {"avgSpeed": {"type": "number", "format": "double", "refType": null}, "carName": {"type": "string", "refType": null}, "date": {"type": "string", "refType": null}, "idlingCount": {"type": "integer", "format": "int32", "refType": null}, "idlingDuration": {"type": "integer", "format": "int64", "refType": null}, "maxSpeed": {"type": "number", "format": "double", "refType": null}, "parkingCount": {"type": "integer", "format": "int32", "refType": null}, "parkingDuration": {"type": "integer", "format": "int64", "refType": null}, "tripCount": {"type": "integer", "format": "int32", "refType": null}, "tripDuration": {"type": "integer", "format": "int64", "refType": null}, "tripMileage": {"type": "integer", "format": "int32", "refType": null}}, "title": "DrivingSummaryResultDto"}, "EmsAlarmMessageParamDto": {"type": "object", "properties": {"alarmTypes": {"type": "array", "items": {"type": "string"}, "refType": "string"}, "autoIndex": {"type": "integer", "format": "int64", "refType": null}}, "title": "EmsAlarmMessageParamDto"}, "EmsBindDeviceDto": {"type": "object", "properties": {"deviceId": {"type": "string", "description": "device ID", "refType": null}, "vehicleId": {"type": "integer", "format": "int32", "description": "vehicle Id", "refType": null}}, "title": "EmsBindDeviceDto"}, "EmsCarTrackDto": {"type": "object", "required": ["vehicleId"], "properties": {"endTime": {"type": "string", "description": "End Time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "startTime": {"type": "string", "description": "Start time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "timeOffset": {"type": "integer", "format": "int32", "description": "Standard time difference (seconds)", "refType": null}, "vehicleId": {"type": "integer", "format": "int32", "description": "vehicle ID", "refType": null}}, "title": "EmsCarTrackDto"}, "EmsCarTrackTokenDto": {"type": "object", "required": ["vehicleId"], "properties": {"endTime": {"type": "string", "description": "End Time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "startTime": {"type": "string", "description": "Start time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "timeOffset": {"type": "integer", "format": "int32", "description": "Standard time difference (seconds)", "refType": null}, "token": {"type": "string", "refType": null}, "vehicleId": {"type": "integer", "format": "int32", "description": "vehicle ID", "refType": null}}, "title": "EmsCarTrackTokenDto"}, "EmsDeviceTypeParamDto": {"type": "object", "properties": {"comment": {"type": "string", "refType": null}, "deviceId": {"type": "string", "refType": null}, "deviceType": {"type": "string", "refType": null}, "expireTime": {"type": "integer", "format": "int32", "refType": null}, "imei": {"type": "string", "refType": null}, "simCode": {"type": "string", "refType": null}, "status": {"type": "integer", "format": "int32", "refType": null}, "unlockPassword": {"type": "string", "refType": null}}, "title": "EmsDeviceTypeParamDto"}, "EmsForgotPasswordDto": {"type": "object", "properties": {"code": {"type": "string", "refType": null}, "email": {"type": "string", "refType": null}, "username": {"type": "string", "refType": null}}, "title": "EmsForgotPasswordDto"}, "EmsGeofenceDto": {"type": "object", "properties": {"carId": {"type": "integer", "format": "int32", "refType": null}, "color": {"type": "integer", "format": "int32", "refType": null}, "distance": {"type": "integer", "format": "int32", "refType": null}, "geofenceId": {"type": "integer", "format": "int32", "refType": null}, "geofenceName": {"type": "string", "refType": null}, "geofenceType": {"type": "integer", "format": "int32", "refType": null}, "latlngs": {"type": "string", "refType": null}}, "title": "EmsGeofenceDto"}, "EmsGeofence对象": {"type": "object", "properties": {"clientId": {"type": "integer", "format": "int32", "refType": null}, "color": {"type": "integer", "format": "int32", "refType": null}, "distance": {"type": "integer", "format": "int32", "refType": null}, "geofenceId": {"type": "integer", "format": "int32", "refType": null}, "geofenceName": {"type": "string", "refType": null}, "geofenceType": {"type": "integer", "format": "int32", "refType": null}, "path": {"type": "string", "refType": null}, "test": {"type": "string", "refType": null}, "userId": {"type": "integer", "format": "int32", "refType": null}}, "title": "EmsGeofence对象", "description": "围栏表"}, "EmsMongoTrackModel": {"type": "object", "properties": {"acc": {"type": "integer", "format": "int32", "refType": null}, "backCapStatus": {"type": "integer", "format": "int32", "refType": null}, "carId": {"type": "integer", "format": "int32", "refType": null}, "devBatteryPCT": {"type": "integer", "format": "int32", "refType": null}, "devId": {"type": "string", "refType": null}, "devTemp": {"type": "integer", "format": "int32", "refType": null}, "devType": {"type": "integer", "format": "int32", "refType": null}, "direction": {"type": "string", "refType": null}, "event": {"type": "string", "refType": null}, "exBytes": {"type": "string", "refType": null}, "gpsCount": {"type": "integer", "format": "int32", "refType": null}, "gpsTime": {"type": "integer", "format": "int64", "refType": null}, "gsmMode": {"type": "string", "refType": null}, "gsmSignal": {"type": "integer", "format": "int32", "refType": null}, "gtime": {"type": "string", "refType": null}, "lat": {"type": "number", "format": "double", "refType": null}, "lng": {"type": "number", "format": "double", "refType": null}, "locate": {"type": "integer", "format": "int32", "refType": null}, "lockStatus": {"type": "integer", "format": "int32", "refType": null}, "mileage": {"type": "integer", "format": "int32", "refType": null}, "motorLockStatus": {"type": "integer", "format": "int32", "refType": null}, "online": {"type": "boolean", "refType": null}, "speed": {"type": "integer", "format": "int32", "refType": null}, "status": {"type": "string", "refType": null}, "steelStringStatus": {"type": "integer", "format": "int32", "refType": null}, "todayIdlingSeconds": {"type": "integer", "format": "int64", "refType": null}, "todayMaxSpeed": {"type": "integer", "format": "int32", "refType": null}, "updateTime": {"type": "integer", "format": "int64", "refType": null}, "utime": {"type": "string", "refType": null}}, "title": "EmsMongoTrackModel"}, "EmsPasswordUnlockDto": {"type": "object", "required": ["deviceId", "password"], "properties": {"deviceId": {"type": "string", "description": "device ID", "refType": null}, "password": {"type": "string", "description": "Lock code", "refType": null}}, "title": "EmsPasswordUnlockDto"}, "EmsRemoteCommandDto": {"type": "object", "required": ["commandType", "deviceId"], "properties": {"commandType": {"type": "integer", "format": "int32", "description": "Command type, 1: Remote unlocking; 2: Remote disconnection of oil and electricity; 3: Remote recovery of oil and electricity;4: set speed", "refType": null}, "deviceId": {"type": "string", "description": "device ID", "refType": null}, "second": {"type": "integer", "format": "int32", "refType": null}, "speed": {"type": "integer", "format": "int32", "refType": null}}, "title": "EmsRemoteCommandDto"}, "EmsRemoteResultDto": {"type": "object", "required": ["commandType", "deviceId", "id"], "properties": {"commandType": {"type": "integer", "format": "int32", "description": "Command type, 1: Remote unlocking; 2: Remote disconnection of oil and electricity; 3: Remote recovery of oil and electricity4: set speed", "refType": null}, "deviceId": {"type": "string", "description": "device ID", "refType": null}, "id": {"type": "integer", "format": "int32", "description": "Instruction Id", "refType": null}}, "title": "EmsRemoteResultDto"}, "EmsSendCommandDto": {"type": "object", "required": ["command", "commandType", "deviceIds"], "properties": {"command": {"type": "string", "description": "command", "refType": null}, "commandType": {"type": "integer", "format": "int32", "description": "Instruction type, 1: Text type; 2: Hex type (hexadecimal type)", "refType": null}, "deviceIds": {"type": "array", "description": "Device Id Collection", "items": {"type": "string"}, "refType": "string"}}, "title": "EmsSendCommandDto"}, "EmsSendCommandResultDto": {"type": "object", "required": ["deviceIds", "id"], "properties": {"deviceIds": {"type": "array", "description": "Supported deviceIds", "items": {"type": "string"}, "refType": "string"}, "id": {"type": "integer", "format": "int32", "description": "ID, which is used to obtain the response of the instruction", "refType": null}}, "title": "EmsSendCommandResultDto"}, "EmsSimpleDeviceDto": {"type": "object", "properties": {"acc": {"type": "integer", "format": "int32", "refType": null}, "backCapStatus": {"type": "integer", "format": "int32", "refType": null}, "devBatteryPCT": {"type": "integer", "format": "int32", "refType": null}, "devTemp": {"type": "integer", "format": "int32", "refType": null}, "devType": {"type": "integer", "format": "int32", "refType": null}, "devTypeName": {"type": "string", "refType": null}, "deviceId": {"type": "string", "description": "device ID", "refType": null}, "direction": {"type": "string", "refType": null}, "event": {"type": "string", "refType": null}, "exBytes": {"type": "string", "refType": null}, "gpsTime": {"type": "string", "refType": null}, "groupName": {"type": "string", "refType": null}, "gsmMode": {"type": "string", "refType": null}, "lat": {"type": "number", "format": "double", "refType": null}, "lng": {"type": "number", "format": "double", "refType": null}, "locate": {"type": "integer", "format": "int32", "refType": null}, "lockStatus": {"type": "integer", "format": "int32", "refType": null}, "mileage": {"type": "integer", "format": "int32", "refType": null}, "motorLockStatus": {"type": "integer", "format": "int32", "refType": null}, "online": {"type": "boolean", "refType": null}, "speed": {"type": "integer", "format": "int32", "refType": null}, "status": {"type": "string", "refType": null}, "steelStringStatus": {"type": "integer", "format": "int32", "refType": null}, "todayIdlingSeconds": {"type": "integer", "format": "int64", "refType": null}, "todayMaxSpeed": {"type": "integer", "format": "int32", "refType": null}, "updateTime": {"type": "string", "refType": null}, "vehicleId": {"type": "integer", "format": "int32", "description": "Car ID", "refType": null}, "vehicleName": {"type": "string", "description": "Car name", "refType": null}}, "title": "EmsSimpleDeviceDto"}, "EmsTrackDto": {"type": "object", "properties": {"acc": {"type": "integer", "format": "int32", "refType": null}, "carId": {"type": "integer", "format": "int32", "refType": null}, "devBatteryPCT": {"type": "integer", "format": "int32", "refType": null}, "devType": {"type": "integer", "format": "int32", "refType": null}, "deviceId": {"type": "string", "refType": null}, "direction": {"type": "string", "refType": null}, "event": {"type": "string", "refType": null}, "exBytes": {"type": "string", "refType": null}, "gpsCount": {"type": "integer", "format": "int32", "refType": null}, "gpsTime": {"type": "string", "refType": null}, "gpsTimeSecond": {"type": "integer", "format": "int64", "refType": null}, "gsmMode": {"type": "string", "refType": null}, "gsmSignal": {"type": "integer", "format": "int32", "refType": null}, "lat": {"type": "number", "format": "double", "refType": null}, "lng": {"type": "number", "format": "double", "refType": null}, "locate": {"type": "integer", "format": "int32", "refType": null}, "mileage": {"type": "integer", "format": "int32", "refType": null}, "speed": {"type": "integer", "format": "int32", "refType": null}, "status": {"type": "string", "refType": null}, "todayMaxSpeed": {"type": "integer", "format": "int32", "refType": null}, "updateTime": {"type": "string", "refType": null}}, "title": "EmsTrackDto"}, "EmsTrackNewPointsParamDto": {"type": "object", "properties": {"carId": {"type": "array", "items": {"type": "integer", "format": "int32"}, "refType": "integer"}, "timeOffset": {"type": "integer", "format": "int32", "description": "Standard time difference (seconds)", "refType": null}}, "title": "EmsTrackNewPointsParamDto"}, "EmsTrackParamDto": {"type": "object", "properties": {"carId": {"type": "array", "items": {"type": "integer", "format": "int32"}, "refType": "integer"}, "endTime": {"type": "string", "description": "End Time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "startTime": {"type": "string", "description": "Start time(yyyy-MM-dd HH:mm:ss)", "refType": null}, "timeOffset": {"type": "integer", "format": "int32", "description": "Standard time difference (seconds)", "refType": null}}, "title": "EmsTrackParamDto"}, "FatigueDrivingDto": {"type": "object", "required": ["alarmType", "carName"], "properties": {"alarmType": {"type": "string", "description": "Alarm type", "refType": null}, "carName": {"type": "string", "description": "carName", "refType": null}, "duration": {"type": "integer", "format": "int64", "description": "Duration", "refType": null}, "endLat": {"type": "number", "format": "double", "description": "End latitude", "refType": null}, "endLng": {"type": "number", "format": "double", "description": "End longitude", "refType": null}, "endTime": {"type": "string", "description": "End time", "refType": null}, "rfidNum": {"type": "string", "description": "driverRFID", "refType": null}, "startLat": {"type": "number", "format": "double", "description": "Start latitude", "refType": null}, "startLng": {"type": "number", "format": "double", "description": "Start longitude", "refType": null}, "startTime": {"type": "string", "description": "Start time", "refType": null}}, "title": "FatigueDrivingDto"}, "HarshAccelerationDto": {"type": "object", "properties": {"alarmType": {"type": "string", "description": "Alarm type", "refType": null}, "carName": {"type": "string", "description": "carName", "refType": null}, "dateTime": {"type": "string", "description": "Time", "refType": null}, "lat": {"type": "number", "format": "double", "description": "latitude", "refType": null}, "lng": {"type": "number", "format": "double", "description": "longitude", "refType": null}, "rfidNum": {"type": "string", "description": "driverRFID", "refType": null}}, "title": "HarshAccelerationDto"}, "HarshBrakeDto": {"type": "object", "properties": {"alarmType": {"type": "string", "description": "Alarm type", "refType": null}, "carName": {"type": "string", "description": "carName", "refType": null}, "dateTime": {"type": "string", "description": "Time", "refType": null}, "lat": {"type": "number", "format": "double", "description": "latitude", "refType": null}, "lng": {"type": "number", "format": "double", "description": "longitude", "refType": null}, "rfidNum": {"type": "string", "description": "driverRFID", "refType": null}}, "title": "HarshBrakeDto"}, "OffsetTime": {"type": "object", "properties": {"timeOffset": {"type": "integer", "format": "int32", "description": "Standard time difference (seconds)", "refType": null}}, "title": "OffsetTime"}, "OverSpeedDto": {"type": "object", "required": ["alarmType", "carName"], "properties": {"alarmType": {"type": "string", "description": "Alarm type", "refType": null}, "avgSpeed": {"type": "number", "format": "double", "description": "Average velocity", "refType": null}, "carName": {"type": "string", "description": "carName", "refType": null}, "duration": {"type": "integer", "format": "int64", "description": "Duration", "refType": null}, "endLat": {"type": "number", "format": "double", "description": "End latitude", "refType": null}, "endLng": {"type": "number", "format": "double", "description": "End longitude", "refType": null}, "endMileage": {"type": "integer", "format": "int32", "description": "End Mileage", "refType": null}, "endTime": {"type": "string", "description": "End time", "refType": null}, "maxSpeed": {"type": "integer", "format": "int32", "description": "Maximum speed", "refType": null}, "rfidNum": {"type": "string", "description": "driverRFID", "refType": null}, "startLat": {"type": "number", "format": "double", "description": "Start latitude", "refType": null}, "startLng": {"type": "number", "format": "double", "description": "Start longitude", "refType": null}, "startMileage": {"type": "integer", "format": "int32", "description": "Start Mileage", "refType": null}, "startTime": {"type": "string", "description": "Start time", "refType": null}}, "title": "OverSpeedDto"}, "PositionDataDto": {"type": "object", "properties": {"acc": {"type": "integer", "format": "int32", "refType": null}, "carId": {"type": "integer", "format": "int32", "refType": null}, "devId": {"type": "string", "refType": null}, "devType": {"type": "integer", "format": "int32", "refType": null}, "direction": {"type": "integer", "format": "int32", "refType": null}, "exBytes": {"type": "string", "refType": null}, "gpsTime": {"type": "integer", "format": "int64", "refType": null}, "gpsTimeStr": {"type": "string", "refType": null}, "lat": {"type": "string", "refType": null}, "lng": {"type": "string", "refType": null}, "locate": {"type": "integer", "format": "int32", "refType": null}, "mileage": {"type": "integer", "format": "int32", "refType": null}, "speed": {"type": "integer", "format": "int32", "refType": null}, "status": {"type": "string", "refType": null}, "updateTime": {"type": "integer", "format": "int64", "refType": null}}, "title": "PositionDataDto"}, "ResultAlarmListDto": {"type": "object", "properties": {"alarm": {"type": "array", "items": {"$ref": "#/definitions/AlarmListDto", "originalRef": "AlarmListDto"}, "refType": "AlarmListDto"}, "timeIndex": {"type": "integer", "format": "int64", "refType": null}}, "title": "ResultAlarmListDto"}, "ResultAlarmReportDto": {"type": "object", "properties": {"almTime": {"type": "integer", "format": "int64", "refType": null}, "almTimeStr": {"type": "string", "refType": null}, "almType": {"type": "integer", "format": "int32", "refType": null}, "carId": {"type": "integer", "format": "int32", "refType": null}, "deviceType": {"type": "string", "refType": null}, "extData": {"type": "string", "refType": null}, "operation": {"type": "integer", "format": "int32", "refType": null}, "origin": {"type": "integer", "format": "int32", "refType": null}, "positionData": {"type": "string", "refType": null}, "result": {"type": "string", "refType": null}, "userName": {"type": "string", "refType": null}}, "title": "ResultAlarmReportDto"}, "ResultGroupDrivingBehaviorDto": {"type": "object", "properties": {"carId": {"type": "integer", "format": "int32", "description": "Car Id", "refType": null}, "carName": {"type": "string", "description": "carName", "refType": null}, "collision": {"type": "integer", "format": "int32", "description": "Collision times", "refType": null}, "collisions": {"type": "array", "description": "Collision data", "items": {"$ref": "#/definitions/DrivingCollisionDto", "originalRef": "DrivingCollisionDto"}, "refType": "DrivingCollisionDto"}, "corneringOverSpeed": {"type": "integer", "format": "int32", "description": "Cornering over speed times", "refType": null}, "corneringOverSpeeds": {"type": "array", "description": "Cornering over speed data", "items": {"$ref": "#/definitions/CorneringOverSpeedDto", "originalRef": "CorneringOverSpeedDto"}, "refType": "CorneringOverSpeedDto"}, "driverName": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "refType": null}, "driverRFID": {"type": "string", "description": "driverRFID", "refType": null}, "fatigueDriving": {"type": "integer", "format": "int32", "description": "Fatigue driving times", "refType": null}, "fatigueDrivings": {"type": "array", "description": "Fatigue driving data", "items": {"$ref": "#/definitions/FatigueDrivingDto", "originalRef": "FatigueDrivingDto"}, "refType": "FatigueDrivingDto"}, "harshAcceleration": {"type": "integer", "format": "int32", "description": "Hash acceleration times", "refType": null}, "harshAccelerations": {"type": "array", "description": "Hash acceleration data", "items": {"$ref": "#/definitions/HarshAccelerationDto", "originalRef": "HarshAccelerationDto"}, "refType": "HarshAccelerationDto"}, "harshBrake": {"type": "integer", "format": "int32", "description": "Harsh brake times", "refType": null}, "harshBrakes": {"type": "array", "description": "Harsh brake data", "items": {"$ref": "#/definitions/HarshBrakeDto", "originalRef": "HarshBrakeDto"}, "refType": "HarshBrakeDto"}, "idle": {"type": "integer", "format": "int32", "description": "Idle times", "refType": null}, "idles": {"type": "array", "description": "Idle data", "items": {"$ref": "#/definitions/ResultParkingReportDto", "originalRef": "ResultParkingReportDto"}, "refType": "ResultParkingReportDto"}, "overSpeed": {"type": "integer", "format": "int32", "description": "Over speed times", "refType": null}, "overSpeeds": {"type": "array", "description": "Over speed data", "items": {"$ref": "#/definitions/OverSpeedDto", "originalRef": "OverSpeedDto"}, "refType": "OverSpeedDto"}}, "title": "ResultGroupDrivingBehaviorDto"}, "ResultGroupDrivingDailyMileagesDto«drivingReport对象»": {"type": "object", "properties": {"dayCount": {"type": "integer", "format": "int32", "refType": null}, "detail": {"type": "array", "items": {"$ref": "#/definitions/drivingReport对象", "originalRef": "drivingReport对象"}, "refType": "drivingReport对象"}, "totalMileage": {"type": "number", "format": "double", "refType": null}, "vehicleName": {"type": "string", "refType": null}}, "title": "ResultGroupDrivingDailyMileagesDto«drivingReport对象»"}, "ResultGroupDrivingSummaryDto«DrivingSummaryResultDto»": {"type": "object", "required": ["carName"], "properties": {"avgSpeed": {"type": "number", "format": "double", "refType": null}, "carName": {"type": "string", "description": "carName", "refType": null}, "data": {"type": "array", "items": {"$ref": "#/definitions/DrivingSummaryResultDto", "originalRef": "DrivingSummaryResultDto"}, "refType": "DrivingSummaryResultDto"}, "idlingCount": {"type": "integer", "format": "int32", "refType": null}, "idlingDuration": {"type": "integer", "format": "int64", "refType": null}, "maxSpeed": {"type": "number", "format": "double", "refType": null}, "parkingCount": {"type": "integer", "format": "int32", "refType": null}, "parkingDuration": {"type": "integer", "format": "int64", "refType": null}, "tripCount": {"type": "integer", "format": "int32", "refType": null}, "tripDuration": {"type": "integer", "format": "int64", "refType": null}, "tripMileage": {"type": "integer", "format": "int32", "refType": null}}, "title": "ResultGroupDrivingSummaryDto«DrivingSummaryResultDto»"}, "ResultGroupParkingReportDto": {"type": "object", "required": ["carName", "rfidNum"], "properties": {"carId": {"type": "integer", "format": "int32", "description": "Car Id", "refType": null}, "carName": {"type": "string", "description": "carName", "refType": null}, "count": {"type": "integer", "format": "int32", "refType": null}, "data": {"type": "array", "items": {"$ref": "#/definitions/ResultParkingReportDto", "originalRef": "ResultParkingReportDto"}, "refType": "ResultParkingReportDto"}, "driverName": {"type": "string", "description": "Car Id", "refType": null}, "duration": {"type": "integer", "format": "int64", "refType": null}, "rfidNum": {"type": "string", "description": "RFID Num", "refType": null}}, "title": "ResultGroupParkingReportDto"}, "ResultGroupRunReportDto": {"type": "object", "required": ["carName"], "properties": {"avgSpeed": {"type": "number", "format": "double", "refType": null}, "carName": {"type": "string", "description": "carName", "refType": null}, "count": {"type": "integer", "format": "int32", "refType": null}, "data": {"type": "array", "items": {"$ref": "#/definitions/RunReportResultDto", "originalRef": "RunReportResultDto"}, "refType": "RunReportResultDto"}, "duration": {"type": "integer", "format": "int64", "refType": null}, "maxSpeed": {"type": "number", "format": "double", "refType": null}, "mileage": {"type": "integer", "format": "int64", "refType": null}}, "title": "ResultGroupRunReportDto"}, "ResultParkingReportDto": {"type": "object", "required": ["carName"], "properties": {"alarmType": {"type": "string", "description": "Alarm type", "refType": null}, "carId": {"type": "integer", "format": "int32", "description": "Car Id", "refType": null}, "carName": {"type": "string", "description": "carName", "refType": null}, "driverName": {"type": "string", "refType": null}, "duration": {"type": "integer", "format": "int64", "refType": null}, "endFuel": {"type": "integer", "format": "int32", "description": "End fuel quantity", "refType": null}, "endTime": {"type": "string", "description": "End time", "refType": null}, "fuel": {"type": "integer", "format": "int32", "description": "Single fuel consumption", "refType": null}, "lat": {"type": "number", "format": "double", "refType": null}, "lng": {"type": "number", "format": "double", "refType": null}, "refuelVolume": {"type": "integer", "format": "int32", "description": "This refueling volume", "refType": null}, "rfidNum": {"type": "string", "refType": null}, "startFuel": {"type": "integer", "format": "int32", "description": "Start fuel quantity", "refType": null}, "startTime": {"type": "string", "description": "Start time", "refType": null}}, "title": "ResultParkingReportDto"}, "RmsAlarmParamDto": {"type": "object", "properties": {"timeIndex": {"type": "integer", "format": "int32", "refType": null}, "timeOffset": {"type": "integer", "format": "int32", "description": "Standard time difference (seconds)", "refType": null}}, "title": "RmsAlarmParamDto"}, "RunReportResultDto": {"type": "object", "required": ["carName"], "properties": {"avgSpeed": {"type": "number", "format": "double", "refType": null}, "carId": {"type": "integer", "format": "int32", "refType": null}, "carName": {"type": "string", "description": "车牌号码", "refType": null}, "driverName": {"type": "string", "refType": null}, "endDateTime": {"type": "string", "refType": null}, "endLat": {"type": "number", "format": "double", "refType": null}, "endLng": {"type": "number", "format": "double", "refType": null}, "endMileage": {"type": "integer", "format": "int32", "refType": null}, "maxSpeed": {"type": "number", "format": "double", "refType": null}, "mileage": {"type": "integer", "format": "int32", "refType": null}, "startDateTime": {"type": "string", "refType": null}, "startLat": {"type": "number", "format": "double", "refType": null}, "startLng": {"type": "number", "format": "double", "refType": null}, "startMileage": {"type": "integer", "format": "int32", "refType": null}, "tripDuration": {"type": "integer", "format": "int64", "refType": null}}, "title": "RunReportResultDto"}, "UmsJgDto": {"type": "object", "properties": {"jgId": {"type": "string", "refType": null}}, "title": "UmsJgDto"}, "UmsUserLoginDto": {"type": "object", "required": ["password", "username"], "properties": {"password": {"type": "string", "description": "password", "refType": null}, "username": {"type": "string", "description": "user name", "refType": null}}, "title": "UmsUserLoginDto"}, "UmsUserPasswordDto": {"type": "object", "required": ["confirmPassword", "newPassword", "oldPassword"], "properties": {"confirmPassword": {"type": "string", "description": "Confirm Password", "refType": null}, "newPassword": {"type": "string", "description": "Password", "refType": null}, "oldPassword": {"type": "string", "description": "Old password", "refType": null}}, "title": "UmsUserPasswordDto"}, "drivingReport对象": {"type": "object", "properties": {"carId": {"type": "integer", "format": "int32", "refType": null}, "carName": {"type": "string", "refType": null}, "endMileage": {"type": "number", "format": "double", "refType": null}, "gpsTime": {"type": "string", "refType": null}, "mileage": {"type": "number", "format": "double", "refType": null}, "startMileage": {"type": "number", "format": "double", "refType": null}}, "title": "drivingReport对象", "description": "drivingReport数据表"}, "drivingReport对象_1": {"type": "object", "properties": {"avgSpeed": {"type": "number", "format": "double", "refType": null}, "carId": {"type": "integer", "format": "int32", "refType": null}, "driverName": {"type": "string", "refType": null}, "driverRFID": {"type": "string", "refType": null}, "durationSeconds": {"type": "integer", "format": "int32", "refType": null}, "endDatetime": {"type": "integer", "format": "int64", "refType": null}, "endFuelLiters": {"type": "integer", "format": "int32", "refType": null}, "endLat": {"type": "number", "format": "double", "refType": null}, "endLng": {"type": "number", "format": "double", "refType": null}, "endMileage": {"type": "integer", "format": "int32", "refType": null}, "endTime": {"type": "string", "refType": null}, "gpsDataCount": {"type": "integer", "format": "int32", "refType": null}, "maxSpeed": {"type": "number", "format": "double", "refType": null}, "reportType": {"type": "integer", "format": "int32", "refType": null}, "startDatetime": {"type": "integer", "format": "int64", "refType": null}, "startFuelLiters": {"type": "integer", "format": "int32", "refType": null}, "startLat": {"type": "number", "format": "double", "refType": null}, "startLng": {"type": "number", "format": "double", "refType": null}, "startMileage": {"type": "integer", "format": "int32", "refType": null}, "startTime": {"type": "string", "refType": null}}, "title": "drivingReport对象_1", "description": "drivingReport数据表"}, "车辆进出围栏报表": {"type": "object", "properties": {"carId": {"type": "integer", "format": "int32", "refType": null}, "carName": {"type": "string", "refType": null}, "geofenceId": {"type": "integer", "format": "int32", "refType": null}, "geofenceName": {"type": "string", "refType": null}, "inPositionData": {"type": "string", "refType": null}, "inTime": {"type": "integer", "format": "int32", "refType": null}, "inTimeStr": {"type": "string", "refType": null}, "outPositionData": {"type": "string", "refType": null}, "outTime": {"type": "integer", "format": "int32", "refType": null}, "outTimeStr": {"type": "string", "refType": null}}, "title": "车辆进出围栏报表", "description": "车辆进出围栏报表"}, "驾驶行为习惯对象": {"type": "object", "properties": {"avgSpeed": {"type": "number", "format": "double", "refType": null}, "carId": {"type": "integer", "format": "int32", "refType": null}, "corneringName": {"type": "string", "refType": null}, "driverName": {"type": "string", "refType": null}, "driverRFID": {"type": "string", "refType": null}, "durationSeconds": {"type": "integer", "format": "int64", "refType": null}, "endDatetime": {"type": "integer", "format": "int64", "refType": null}, "endLat": {"type": "number", "format": "double", "refType": null}, "endLng": {"type": "number", "format": "double", "refType": null}, "endMileage": {"type": "integer", "format": "int32", "refType": null}, "endSpeed": {"type": "integer", "format": "int32", "refType": null}, "endTime": {"type": "string", "refType": null}, "maxSpeed": {"type": "integer", "format": "int32", "refType": null}, "reportType": {"type": "integer", "format": "int32", "refType": null}, "startDatetime": {"type": "integer", "format": "int64", "refType": null}, "startLat": {"type": "number", "format": "double", "refType": null}, "startLng": {"type": "number", "format": "double", "refType": null}, "startMileage": {"type": "integer", "format": "int32", "refType": null}, "startSpeed": {"type": "integer", "format": "int32", "refType": null}, "startTime": {"type": "string", "refType": null}}, "title": "驾驶行为习惯对象", "description": "驾驶行为习惯数据表"}}, "x-openapi": {"x-markdownFiles": null, "x-setting": {"language": "en-US", "enableSwaggerModels": true, "swaggerModelName": "Swagger Models", "enableReloadCacheParameter": false, "enableAfterScript": true, "enableDocumentManage": true, "enableVersion": false, "enableRequestCache": true, "enableFilterMultipartApis": false, "enableFilterMultipartApiMethodType": "POST", "enableHost": false, "enableHostText": "", "enableDynamicParameter": false, "enableDebug": true, "enableFooter": true, "enableFooterCustom": false, "footerCustomContent": null, "enableSearch": true, "enableOpenApi": true, "enableHomeCustom": false, "homeCustomLocation": null, "enableGroup": true, "enableResponseCode": true}}}