e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
PEDOMAN INTEGRASI APLIKASI (PIA)
e-Seal Monitoring System (eMS) v.2
Oleh :
TIM TEKNIS eMS
Oktober, 2024
P E R H A T I A N
Segala informasi yang tersaji didalam dokumen ini adalah
milik Tim Teknis eMS Direktorat Jenderal Bea dan Cukai dan
bersifat rahasia untuk kalangan perusahaan. Penggunaan dokumen
yang tidak sesuai dengan non-disclossure agreement adalah tidak
diperbolehkan.
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
A. BACKGROUND
Pedoman Integrasi Aplikasi (PIA) merupakan salah satu referensi dalam pembentukan program
Application Programming Interface (API) antara e-Seal Monitoring System dan server milik vendor e-seal.
Di dalam pedoman ini dijelaskan hal-hal yang perlu diketahui dalam mengintegrasikan modul-modul pada
eMS Application Programming Interface (API), khususnya menyangkut struktur elemen data yang perlu
dipersiapkan disisi vendor e-seal yang akan dikirimkan ke API Services eMS.
Dokumen Pedoman Integrasi Aplikasi (PIA) ini dibuat dengan tujuan :
- Mempermudah masing-masing pihak yang terkait dengan sistem eMS melakukan integrasi
sistem yang standard & terstruktur
- Sebagai pedoman untuk pengiriman data transaksi secara realtime
- Mengurangi potensi human-error akibat adanya entry data maupun double entry data secara
manual
- Penyajian data yang lebih cepat, tepat, dan akurat di sistem aplikasi eMS maupun yang terkait
dengan eMS
Secara umum dokumen PIA ini memuat:
- Bisnis Proses Integrasi
- Teknis Integrasi eMS
- Contoh Elemen Data
- Contoh Jsonfile
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
B. GLOBAL DESIGN
Secara umum, API Services eMS ini merupakan service pertukaran data antara Direktorat Jenderal Bea
dan Cukai dengan penyedia (vendor) e-seal. Data terkait kontainer dan e-seal dari setiap vendor e-seal
akan dikirimkan ke API Services eMS untuk kemudian digunakan dalam aplikasi monitoring berupa webbased application maupun mobile application yang akan digunakan oleh pegawai Direktorat Jenderal Bea
dan Cukai.
Selain menerima data dari vendor e-seal, API Services eMS ini juga akan mengirimkan data dokumen
kepabeanan berdasarkan request parameter. Data yang diberikan terbatas untuk masing-masing vendor
e-seal.
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
Legend:
Keterangan:
Integrasi aplikasi atau pertukaran data antara sistem eMS dengan sistem vendor e-seal dilakukan
menggunakan protokol HTTP (APPLICATION PROGRAMMING INTERFACE) dengan format dokumen JSON.
1. Vendor e-seal mengirimkan data e-seal yang akan diterima oleh sistem eMS.
2. Vendor e-seal mengirimkan request data dokumen kepabeanan kepada sistem eMS berdasarkan
parameter tertentu.
3. Vendor e-seal mengirimkan data tracking e-seal yang akan diterima oleh sistem eMS. Data ini
dikirimkan cukup sekali saja setiap suatu e-seal akan mulai perjalanannya.
4. Vendor e-seal mengirimkan lokasi dan status e-seal secara periodik
1. Kirim Data e-Seal
VENDOR e-SEAL
 EsealAdd
e-SEAL MONITORING SYSTEM
 receiveDataEseal
 receivePosisiton EsealUpdatePosisiton
4. Kirim Posisi e-Seal (periodik)
3. Kirim Data Tracking
 receiveDataTracking TrackingAdd
 reqDokPabean
V E N D O R
getDokPabean
Web Service Client
2. Request Dokumen Pabean
Web Service Server
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
C. PROCESS SPECIFICATION
Pertukaran data (host to host) JSON antara sistem eMS dengan sistem milik vendor e-seal menggunakan
REST API sebagai protokol komunikasi dan format data JSON.
Berikut ini daftar Method dalam services e-Seal Monitoring System (eMS).
No Fungsi URL Development URL Production Endpoint Method
1 Pengiriman
data e-seal
dari sistem
vendor eseal ke
sistem eMS.
https://apisdev-gw.
beacukai.go.id/trackingeseal
https://apis-gw.
beacukai.go.id/trackingeseal
eseal/add POST
2 Permintaan
data
dokumen
kepabeanan
dari sistem
vendor eseal ke
sistem eMS.
https://apisdevgw.beacukai.go.id/dokumeneseal-service
https://apisgw.beacukai.go.id/dokumeneseal-service
eseal/get-dokpabean/
GET
3 Pengiriman
data
tracking
dari sistem
vendor eseal ke
sistem eMS
https://apisdev-gw.
beacukai.go.id/trackingeseal
https://apis-gw.
beacukai.go.id/trackingeseal
tracking/start POST
4 Pengiriman
data posisi
e-seal dari
sistem
vendor eseal ke
sistem eMS.
https://apisdev-gw.
beacukai.go.id/positioneseal
https://apis-gw.
beacukai.go.id/positioneseal
eseal/updateposition
POST
5 Pengiriman
data
tracking
dari sistem
vendor eseal ke
sistem eMS
https://apisdev-gw.
beacukai.go.id/trackingeseal
https://apis-gw.
beacukai.go.id/trackingeseal
tracking/stop POST
6 Pengiriman
data
perubahan
status ehttps://apisdev-gw.
beacukai.go.id/trackingeseal
https://apis-gw.
beacukai.go.id/trackingeseal
eseal/updatestatus-device
POST
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
seal dari
sistem
vendor eseal ke
sistem eMS.
7 Permintaan
data status
tracking
dari sistem
vendor eseal ke
sistem eMS
https://apisdev-gw.
beacukai.go.id/trackingeseal
https://apis-gw.
beacukai.go.id/trackingeseal
tracking/status GET
Dan berikut ini adalah keterangan method beserta uraian lengkap spesifikasi proses fungsi – fungsi yang
ada pada services di atas. Parameter yang menggunakan huruf italic merupakan parameter yang bersifat
mandatory.
Method Name eseal/add Author Tim Teknis eMS
Method Type POST
Method Header Bearer Token; Content-Type:application/json
Description Pengiriman data e-seal dari sistem vendor e-seal ke sistem eMS.
Input Parameters (Request Body)
Parameters Type Length Repeat Description
idVendor an - 1 ID Vendor
merk an - 1 Merk E-seal
model an - 1 Model E-seal
noImei an - 1 Nomor IMEI
tipe an - 1 Tipe E-seal
token an - 1 Token Vendor
Return
Parameters Type Length Repeat Description
status an - 1
Status aksi. Nilai yang mungkin: success,
error
message an - 1 Keterangan dari status
item an - 1
Data yang berhasil dimasukkan.
Ket.: hanya muncul jika aksi sukses.
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
Server Sistem eMS Client Sistem Vendor
Contoh Format JSON (Parameter):
{
"idVendor": "",
"merk": "",
"model": "",
"noImei": "",
"tipe": "",
"token": ""
}
Contoh Format JSON (Return):
{
"status": "success",
"message": "Berhasil menambahkan data ke tabel td_eseal",
"item": {
"idEseal": "",
"merk": "",
"model": "",
"tipe": "",
"idVendor": "",
"noImei": "",
"status": "",
"wkRekam": "",
"wkUpdate": ""
}
}
Method Name eseal/get-dok-pabean Author Tim Teknis eMS
Method Type GET
Method Header Bearer Token; Content-Type:application/json
Description Permintaan data dokumen kepabeanan dari sistem vendor e-seal ke sistem eMS.
Input Parameters (URL Parameters)
Parameters Type Length Repeat Description
nomor_aju an 26 1 Nomor Aju
Return
Parameters Type Length Repeat Description
status an - 1
Status aksi. Nilai yang mungkin: success,
error
message an - 1 Keterangan dari status
items an - n Data dokumen kepabeanan.
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
Ket.: hanya muncul jika aksi sukses.
Server Sistem eMS Client Sistem Vendor
Contoh Request URL Parameter:
?nomor_aju= 16021600008120160415000002
Contoh Format JSON (Return):
{
"status": "success",
 "message": "Berhasil mendapatkan dokumen pabean",
 "item":
 {
 "nomorAju": "",
 "kodeDokumen": "",
 "nomorDaftar": "",
 "tanggalDaftar": "",
 "kodeKantor": "",
 "namaKantor": "",
 "kodeTps": "",
 "namaGudang": "",
 "idPengusaha": "",
 "namaPengusaha": "",
 "uraian": "",
 "kontainer": [
{
"nomorKontainer": "",
"nomorSegel": ""
}
]
 }
}
Method Name tracking/start Author Tim Teknis eMS
Method Type POST
Method Header Bearer Token; Content-Type:application/json
Description Pengiriman data tracking dari sistem vendor e-seal ke sistem eMS
Input Parameters (Request Body)
Parameters Type Length Repeat Description
Detil Data Kontainer 1 Berulang (1) Row
alamatAsal an - 1 Alamat Asal
alamatTujuan an - 1 Alamat Tujuan
idVendor an - 1 ID Vendor
jnsKontainer an - 1
Jenis Kontainer menggunakan referensi
pada Lampiran 3
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
latitudeAsal an - 1 Latitude Asal
latitudeTujuan an - 1 Latitude Tujuan
lokasiAsal an - 1 Lokasi Asal
lokasiTujuan an - 1 Lokasi Tujuan
longitudeAsal an - 1 Longitude Asal
longitudeTujuan an - 1 Longitude Tujuan
noImei an - 1 Nomor IMEI e-Seal
noEseal an - 1
Nomor e-Seal bersifat unik per tracking
yang dapat diperoleh melalui get-dokpabean sesuai kontainer yang
digunakan. Jika pada get-dok-pabean
nomor segel bernilai null maka dapat
diisi secara mandiri dengan format
kodeKantor-kodeDokumen-nomor
noKontainer an - 1 Nomor Kontainer
noPolisi an - 1 Nomor Polisi
token an - 1 Token Vendor
ukKontainer an - 1
Ukuran Kontainer menggunakan
referensi pada Lampiran 4
namaDriver an - 1 Nama Driver
nomorTeleponDriver an - 1 Nomor Telepon Driver
dokumen n Data dokumen
jenisMuat an - 1 Jenis Muat
jumlahKontainer an - 1 Jumlah Kontainer
kodeDokumen an - 1
Kode Dokumen. Untuk dokumen PLP
dapat diisikan “PLP”
kodeKantor an - 1 Kode Kantor
nomorAju 26 - 1 Nomor Aju
nomorDokumen an - 1 Nomor Dokumen
tanggalDokumen Format: yyyy-MM-dd
Return
Parameters Type Length Repeat Description
status an - 1
Status aksi. Nilai yang mungkin: success,
error
message an - 1 Keterangan dari status
item an - 1
Data yang berhasil dimasukkan.
Ket.: hanya muncul jika aksi sukses.
Server Sistem
eMS Client Sistem
Vendor
Contoh Format JSON (Parameter):
{
"alamatAsal": "",
"alamatTujuan": "",
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
"idVendor": "",
"jnsKontainer": "",
"latitudeAsal": "",
"latitudeTujuan": "",
"lokasiAsal": "",
"lokasiTujuan": "",
"longitudeAsal": "",
 "longitudeTujuan": "",
 "noImei": "",
 "noEseal": "",
 "noKontainer": "",
 "noPolisi": "",
 "token": "",
 "ukKontainer": "",
 "namaDriver": "",
 "nomorTeleponDriver": "",
 "dokumen":[
{
"jenisMuat": "",
"jumlahKontainer": "",
"kodeDokumen": "",
"kodeKantor": "",
"nomorAju": "",
"nomorDokumen": "",
"tanggalDokumen": ""
}
]
}
Method Name tracking/stop Author Tim Teknis eMS
Method Type POST
Method Header Bearer Token; Content-Type:application/json
Description Pengiriman data tracking dari sistem vendor e-seal ke sistem eMS
Input Parameters (Request Body)
Parameters Type Length Repeat Description
Detil Data Kontainer 1 Berulang (1) Row
alamatStop an 26 1 Alamat Saat Tracking Selesai
idVendor an 75 1 ID Vendor
latitudeStop an - 1 Latitude Stop
longitudeStop an - 1 Longitude Stop
noImei an - 1 Nomor IMEI
noEseal an - 1
Nomor e-Seal yang digunakan saat
tracking start
token an - 1 Token Vendor
Return
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
Parameters Type Length Repeat Description
status an - 1
Status aksi. Nilai yang mungkin: success,
error
message an - 1 Keterangan dari status
item an - 1
Data yang berhasil dimasukkan.
Ket.: hanya muncul jika aksi sukses.
Server Sistem
eMS Client Sistem
Vendor
Contoh Format JSON (Parameter):
{
"alamatStop": "",
"idVendor": "",
"latitudeStop": "",
"longitudeStop": "",
"noImei": "",
"noEseal": "",
"token": ""
}
Method Name eseal/update-position Author Tim Teknis eMS
Method Type POST
Method Header Bearer Token; Content-Type:application/json
Description Menerima lokasi dan status e-seal secara periodik dari sistem vendor e-seal ke
sistem eMS.
Input Parameters (Request Body)
Parameters Type Length Repeat Description
address an 1000 1 Address
altitude an 50 1 Posisi Altitude
battery an - 1 Daya Baterai
dayaAki an - 1 Daya Aki
event an - 1
Event e-Seal menggunakan referensi
pada Lampiran 2
idVendor an - 1 ID Vendor
kota an - 1 Kota
latitude an - 1 Posisi Latitude
longitude an - 1 Posisi Longitude
noImei an - 1 Nomor IMEI
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
noEseal an - 1
Nomor e-Seal yang digunakan saat
tracking start
provinsi an - 1 Provinsi
speed an - 1 Kecepatan Kontainer
token an - Token Vendor
Return
status an - 1
Status aksi. Nilai yang mungkin: success,
error
message an - 1 Keterangan dari status
Server Sistem
eMS Client Sistem
Vendor
Contoh Format JSON (Parameter):
{
"address": "",
"altitude": "",
"battery": "",
"dayaAki": "",
"event": "",
"idVendor": "",
"kota": "",
"latitude": "",
"longitude": "",
"noImei": "",
"noEseal": "",
"provinsi": "",
"speed": "",
"token": ""
}
Contoh Format JSON (Return):
{
"status": "success",
"message": "Berhasil menerima data posisi"
}
Method Name eseal/update-status-device Author Tim Teknis eMS
Method Type POST
Method Header Bearer Token; Content-Type:application/json 
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
Description Pengiriman data perubahan status e-seal dari sistem vendor e-seal ke sistem eMS.
Input Parameters (Request Body)
Parameters Type Length Repeat Description
idVendor an - 1 ID Vendor
noImei an - 1 Nomor IMEI
status an - 1
Status E-seal menggunakan referensi pada
Lampiran 5
token an - 1 Token Vendor
Return
Parameters Type Length Repeat Description
status an - 1
Status aksi. Nilai yang mungkin: success,
error
message an - 1 Keterangan dari status
item an - 1
Data yang berhasil dimasukkan.
Ket.: hanya muncul jika aksi sukses.
Server Sistem eMS Client Sistem Vendor
Contoh Format JSON (Parameter):
{
"idVendor": "",
"noImei": "",
"status": "",
"token": ""
}
Contoh Format JSON (Return):
{
"status": "success",
"message": "Berhasil mengubah data di tabel td_eseal"
}
Method Name tracking/status Author Tim Teknis eMS
Method Type GET
Method Header Bearer Token; Content-Type:application/json
Description Permintaan data dokumen kepabeanan dari sistem vendor e-seal ke sistem
eMS.
Input Parameters (URL Parameters)
Parameters Type Length Repeat Description
idVendor an 26 1 Nomor Aju
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
noEseal an - 1 Nomor E-Seal
token an - 1 Token
Return
Parameters Type Length Repeat Description
status an - 1
Status aksi. Nilai yang mungkin:
success, error
message an - 1 Keterangan dari status
items an - n
Data dokumen kepabeanan.
Ket.: hanya muncul jika aksi sukses.
Server Sistem eMS Client Sistem Vendor
Contoh Request URL Parameter:
?idVendor=373c041c-f9b8-491f-a4f6-7cc47a0569d3&noEseal=ESEALTEST123098&token=919253c8-d0e1-4780-89d0-e91f77e89855
Contoh Format JSON (Return):
{
 "status": "success",
 "message": "Berhasil mendapatkan data tracking untuk nomor eseal
ESEALTEST-123098",
 "item": {
 "start": "success",
 "updatePosition": 0,
 "stop": "success"
 }
}
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
D. REFERENSI
LAMPIRAN 1 - DATA STATUS RESPON
KODE KETERANGAN
200 Sukses
400 Data Parameter Salah
401 User Akses Tidak Valid
403 Ilegal Inquery / Akses
404 Akses Service Tidak Ditemukan
405 Akses Service Ditolak
500 Proses Payload Data di Sistem eMS Gagal
504 Proses Payload Data di Sistem eMS Timeout
LAMPIRAN 2 – DATA REFERENSI EVENT
KODE KETERANGAN
0 Normal, tidak ada event
1 Locked
2 Unlocked
3 Cut the rope
LAMPIRAN 3 – DATA REFERENSI JENIS KONTAINER
KODE JENIS KONTAINER KETERANGAN
1 General / Dry Cargo
2 Tunne Type
3 Open Top Steel
4 Flat Rack
5 Reefer/Refregete
e-SEAL MONITORING SYSTEM (eMS)
DIREKTORAT JENDERAL BEA DAN CUKAI
CONFIDENTIAL AND PROPRIETARY © 2019. E-Seal Monitoring System (eMS) Direktorat Jenderal Bea dan Cukai
Copying in whole or in part is strictly forbidden without prior written approval
6 Barge Container
7 Bulk Container
8 Isotank
99 Lain-lain
LAMPIRAN 4 – DATA REFERENSI JENIS KONTAINER
KODE UKURAN KONTAINER KETERANGAN
20 20 FEET
40 40 FEET
45 45 FEET
60 60 FEET
LAMPIRAN 5 – DATA REFERENSI STATUS ESEAL
KODE STATUS ESEAL KETERANGAN
0 INACTIVE
1 ACTIVE