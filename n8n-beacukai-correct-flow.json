{"name": "Beacukai Correct Flow - Sesuai Project Implementation", "nodes": [{"parameters": {"httpMethod": "GET", "path": "rest/pub/apigateway/jwt/getJsonWebToken", "responseMode": "responseNode", "options": {}}, "id": "jwt-token-webhook", "name": "JWT Token Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 200], "webhookId": "jwt-token"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.headers.authorization }}", "rightValue": "Basic dmlydHVzRGV2OmFSNyNwTDlxWkExbQ==", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-2", "leftValue": "={{ $json.query.app_id }}", "rightValue": "56c566b2-53ab-4335-8f13-efdbe144ba52", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-jwt-auth", "name": "Validate JWT Auth", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 200]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"jwt\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ2aXJ0dXNEZXYiLCJpYXQiOjE2MzQ1NjcwMDAsImV4cCI6MTYzNDU3MDYwMCwiYXBwX2lkIjoiNTZjNTY2YjItNTNhYi00MzM1LThmMTMtZWZkYmUxNDRiYTUyIn0.mock_signature_for_testing\",\n  \"expires_in\": 3600,\n  \"token_type\": \"Bearer\",\n  \"scope\": \"eseal_api\"\n}", "responseHeaders": {"entries": [{"name": "Set-<PERSON><PERSON>", "value": "JSESSIONID=mock_session_12345; Path=/; HttpOnly"}]}}, "id": "jwt-success-response", "name": "JWT Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 140]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"error\": \"Unauthorized\",\n  \"message\": \"Missing or invalid Authorization header. Required: Basic dmlydHVzRGV2OmFSNyNwTDlxWkExbQ== and app_id=56c566b2-53ab-4335-8f13-efdbe144ba52\"\n}", "responseCode": 401}, "id": "jwt-error-response", "name": "JWT Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 260]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/eseal/add", "responseMode": "responseNode", "options": {}}, "id": "eseal-add-webhook", "name": "E-Seal Add Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 380], "webhookId": "eseal-add"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.idVendor }}", "rightValue": "55028fd4-ba83-44a4-a506-c3c7b8401ffe", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-2", "leftValue": "={{ $json.body.token }}", "rightValue": "919253c8-d0e1-4780-89d0-e91f77e89855", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-3", "leftValue": "={{ $json.body.noImei }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-eseal-add", "name": "Validate E-Seal Add", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 380]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO request_logs (id, \"requestUrl\", \"requestBody\", \"requestHeader\", status, \"responseBody\", \"isSync\", \"createdAt\") VALUES (gen_random_uuid(), 'https://apisdev-gw.beacukai.go.id/tracking-eseal/eseal/add', $1::jsonb, $2::jsonb, 200, $3::jsonb, true, NOW())", "additionalFields": {"mode": "independently"}, "options": {"queryParameters": ["={{ JSON.stringify($json.body) }}", "={{ JSON.stringify($json.headers) }}", "={{ JSON.stringify({\"status\": \"success\", \"message\": \"E-Seal berhasil ditambahkan ke sistem eMS\", \"item\": {\"idEseal\": \"ESEAL_\" + $now.format('x'), \"noImei\": $json.body.noImei, \"idVendor\": $json.body.idVendor, \"merk\": $json.body.merk, \"model\": $json.body.model, \"tipe\": $json.body.tipe, \"registeredAt\": $now.toISOString(), \"status\": \"REGISTERED\"}}) }}"]}}, "id": "save-eseal-add-log", "name": "Save E-Seal Add Log to Neon DB", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 320], "credentials": {"postgres": {"id": "neon-db", "name": "Neon Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"E-Seal berhasil ditambahkan ke sistem eMS\",\n  \"item\": {\n    \"idEseal\": \"ESEAL_{{ $now.format('x') }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"idVendor\": \"{{ $json.body.idVendor }}\",\n    \"merk\": \"{{ $json.body.merk }}\",\n    \"model\": \"{{ $json.body.model }}\",\n    \"tipe\": \"{{ $json.body.tipe }}\",\n    \"registeredAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"REGISTERED\"\n  }\n}"}, "id": "eseal-add-success-response", "name": "E-Seal Add Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 320]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"Data tidak lengkap. Required: idVendor=55028fd4-ba83-44a4-a506-c3c7b8401ffe, token=919253c8-d0e1-4780-89d0-e91f77e89855, noImei\",\n  \"item\": null\n}", "responseCode": 400}, "id": "eseal-add-error-response", "name": "E-Seal Add Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 440]}, {"parameters": {"httpMethod": "POST", "path": "position-eseal/eseal/update-position", "responseMode": "responseNode", "options": {}}, "id": "position-update-webhook", "name": "Position Update Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 560], "webhookId": "position-update"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.idVendor }}", "rightValue": "55028fd4-ba83-44a4-a506-c3c7b8401ffe", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-2", "leftValue": "={{ $json.body.token }}", "rightValue": "919253c8-d0e1-4780-89d0-e91f77e89855", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-3", "leftValue": "={{ $json.body.noImei }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-position-update", "name": "Validate Position Update", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 560]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO request_logs (id, \"requestUrl\", \"requestBody\", \"requestHeader\", status, \"responseBody\", \"isSync\", \"createdAt\") VALUES (gen_random_uuid(), 'https://apisdev-gw.beacukai.go.id/position-eseal/eseal/update-position', $1::jsonb, $2::jsonb, 200, $3::jsonb, true, NOW())", "additionalFields": {"mode": "independently"}, "options": {"queryParameters": ["={{ JSON.stringify($json.body) }}", "={{ JSON.stringify($json.headers) }}", "={{ JSON.stringify({\"status\": \"success\", \"message\": \"Posisi E-Seal berhasil diupdate\", \"item\": {\"noEseal\": $json.body.noEseal, \"noImei\": $json.body.noImei, \"updatedAt\": $now.toISOString(), \"processed\": true}}) }}"]}}, "id": "save-position-update-log", "name": "Save Position Update Log to Neon DB", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 500], "credentials": {"postgres": {"id": "neon-db", "name": "Neon Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Posisi E-Seal berhasil diupdate\",\n  \"item\": {\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"position\": {\n      \"latitude\": \"{{ $json.body.latitude }}\",\n      \"longitude\": \"{{ $json.body.longitude }}\",\n      \"address\": \"{{ $json.body.address }}\",\n      \"speed\": \"{{ $json.body.speed }}\",\n      \"battery\": \"{{ $json.body.battery }}\"\n    },\n    \"updatedAt\": \"{{ $now.toISOString() }}\",\n    \"processed\": true\n  }\n}"}, "id": "position-update-success-response", "name": "Position Update Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 500]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"Data tidak lengkap. Required: idVendor, token, noImei\",\n  \"item\": null\n}", "responseCode": 400}, "id": "position-update-error-response", "name": "Position Update Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 620]}], "connections": {"JWT Token Request": {"main": [[{"node": "Validate JWT Auth", "type": "main", "index": 0}]]}, "Validate JWT Auth": {"main": [[{"node": "JWT Success Response", "type": "main", "index": 0}], [{"node": "JWT Error Response", "type": "main", "index": 0}]]}, "E-Seal Add Request": {"main": [[{"node": "Validate E-Seal Add", "type": "main", "index": 0}]]}, "Validate E-Seal Add": {"main": [[{"node": "Save E-Seal Add Log to Neon DB", "type": "main", "index": 0}], [{"node": "E-Seal Add Error Response", "type": "main", "index": 0}]]}, "Save E-Seal Add Log to Neon DB": {"main": [[{"node": "E-Seal Add Success Response", "type": "main", "index": 0}]]}, "Position Update Request": {"main": [[{"node": "Validate Position Update", "type": "main", "index": 0}]]}, "Validate Position Update": {"main": [[{"node": "Save Position Update Log to Neon DB", "type": "main", "index": 0}], [{"node": "Position Update Error Response", "type": "main", "index": 0}]]}, "Save Position Update Log to Neon DB": {"main": [[{"node": "Position Update Success Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}