# E-Seal Monitor 🔒

![E-Seal Monitor](https://img.shields.io/badge/E--Seal-Monitor-blue?style=for-the-badge)
![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)
![React](https://img.shields.io/badge/React-20232A?style=for-the-badge&logo=react&logoColor=61DAFB)
![Hono](https://img.shields.io/badge/Hono-E36002?style=for-the-badge&logo=hono&logoColor=white)
![Bun](https://img.shields.io/badge/Bun-000000?style=for-the-badge&logo=bun&logoColor=white)

**E-Seal Monitor** adalah sistem monitoring dan manajemen E-Seal (Electronic Seal) yang dibangun dengan teknologi modern. Sistem ini memungkinkan pengelolaan data E-Seal, tracking real-time, dan monitoring status perangkat untuk keperluan kepabeanan dan logistik.

## 🚀 Fitur Utama

### 📊 **Manajemen Data E-Seal**
- **Tambah Data E-Seal**: Input data perangkat E-Seal baru
- **Dokumen <PERSON>aan**: Manajemen dokumen dan data kepabeanan
- **Update Posisi**: Pembaruan lokasi E-Seal secara real-time
- **Update Status Device**: Monitoring dan update status perangkat

### 🗺️ **Tracking & Monitoring**
- **Tracking Start**: Memulai proses tracking E-Seal
- **Tracking Stop**: Menghentikan proses tracking
- **Tracking Status**: Monitoring status tracking real-time
- **Logs System**: Pencatatan aktivitas dan audit trail

### 👥 **Manajemen Sistem**
- **Manajemen Pengguna**: Kelola user dan akses sistem
- **Role Management**: Pengaturan peran dan hak akses
- **Multi-level Authentication**: Super Admin, Admin, dan User roles

## 🛠️ Tech Stack

### **Frontend (Client)**
- **React 19** - Modern UI library dengan hooks terbaru
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool dan dev server
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible component primitives
- **React Router** - Client-side routing
- **Lucide React** - Beautiful icon library

### **Backend (Server)**
- **Hono** - Lightweight web framework
- **Bun** - Fast JavaScript runtime
- **TypeScript** - End-to-end type safety
- **CORS** - Cross-origin resource sharing

### **Shared**
- **TypeScript Types** - Shared type definitions
- **API Contracts** - Type-safe API communication

## 📁 Struktur Project

```
E-Seal Monitor/
├── client/                           # Frontend React Application
│   ├── src/
│   │   ├── components/              # Reusable UI components
│   │   │   ├── ui/                  # Base UI components (shadcn/ui)
│   │   │   ├── Header.tsx           # Application header
│   │   │   ├── Sidebar.tsx          # Navigation sidebar
│   │   │   └── Layout.tsx           # Main layout wrapper
│   │   ├── pages/                   # Page components
│   │   │   └── superadmin/          # Super admin pages
│   │   │       ├── DataESeal/       # E-Seal data management
│   │   │       ├── TrackingData/    # Tracking functionality
│   │   │       ├── Logs/            # System logs
│   │   │       └── PengaturanSistem/ # System settings
│   │   ├── lib/                     # Utility functions
│   │   └── assets/                  # Static assets
│   ├── public/                      # Public assets
│   └── package.json                 # Client dependencies
├── server/                          # Backend API Server
│   ├── src/
│   │   ├── index.ts                 # Main server file
│   │   └── client.ts                # Type-safe client
│   └── package.json                 # Server dependencies
├── shared/                          # Shared TypeScript definitions
│   └── src/
│       └── types/                   # Shared type definitions
│           └── index.ts             # API & data types
├── netlify.toml                     # Netlify deployment config
└── package.json                     # Root workspace config
```

## 🔧 API Endpoints

### **Currently Available**
```typescript
GET    /                       # Hello Hono! - Health check
GET    /hello                  # API test endpoint
GET    /api/eseal              # Get E-Seal data with pagination & search
       ?page=1                 # Page number (default: 1)
       &limit=10               # Items per page (default: 10)
       &search=query           # Search in all fields
```

### **Planned Endpoints** (Coming Soon)
```typescript
POST   /api/eseal              # Create new E-Seal
PUT    /api/eseal/:id          # Update E-Seal data
DELETE /api/eseal/:id          # Delete E-Seal

POST   /api/tracking/start     # Start tracking
POST   /api/tracking/stop      # Stop tracking
GET    /api/tracking/status    # Get tracking status
GET    /api/tracking/logs      # Get tracking logs

GET    /api/users              # Get users
POST   /api/users              # Create user
PUT    /api/users/:id          # Update user
GET    /api/roles              # Get roles
```

## 🏗️ Arsitektur Sistem

### **Frontend Architecture**
- **Component-based**: Modular React components
- **Type-safe**: Full TypeScript integration
- **Responsive**: Mobile-first design dengan Tailwind CSS
- **State Management**: React hooks untuk state lokal
- **Routing**: React Router untuk navigasi SPA

### **Backend Architecture**
- **RESTful API**: Hono framework dengan mock data
- **Type Safety**: Shared types antara client dan server
- **CORS Enabled**: Cross-origin resource sharing
- **Mock Data**: Sample E-Seal data untuk development
- **Pagination**: Built-in pagination dan search functionality
- **Lightweight**: Minimal overhead dengan Bun runtime

## 🚀 Quick Start

### Prerequisites
- **Bun** >= 1.0.0
- **Node.js** >= 18.0.0
- **Git**

### Installation

```bash
# Clone repository
git clone https://github.com/your-username/e-seal-monitor.git
cd e-seal-monitor

# Install dependencies untuk semua workspaces
bun install
```

### Development

```bash
# Jalankan semua services (shared, server, client)
bun run dev

# Atau jalankan individual
bun run dev:shared  # Watch dan compile shared types
bun run dev:server  # Jalankan Hono backend
bun run dev:client  # Jalankan Vite dev server
```

### Building

```bash
# Build semua
bun run build

# Atau build individual
bun run build:shared  # Build shared types package
bun run build:server  # Build server
bun run build:client  # Build React frontend
```

## 🌐 Deployment

### Netlify (Frontend)
Project sudah dikonfigurasi untuk deployment di Netlify:

```bash
# Deploy menggunakan Netlify CLI
npx netlify-cli deploy --prod --dir=client/dist

# Atau push ke GitHub dan connect dengan Netlify
git push origin main
```

### Server Deployment
Server dapat di-deploy ke berbagai platform:
- **Railway**
- **Vercel**
- **Heroku**
- **VPS/Cloud Server**

## 📱 Screenshots & Demo

### Dashboard Super Admin
- Manajemen data E-Seal
- Real-time tracking status
- System logs dan audit trail

### Mobile Responsive
- Optimized untuk mobile devices
- Touch-friendly interface
- Responsive sidebar navigation

## � Data Models

### E-Seal Data Structure
```typescript
type ESealData = {
  id: number;
  perusahaan: string;
  idVendor: string;
  merk: string;
  model: string;
  nomorIMEI: string;
  tipeESeal: string;
  token: string;
  jarakTempuh: string;
  status: 'Unlocked' | 'Locked';
}
```

### API Response Structure
```typescript
type ApiResponse = {
  message: string;
  success: boolean;
}

type ESealResponse = {
  data: ESealData[];
  total: number;
  page: number;
  limit: number;
}
```

### Sample Mock Data
Server saat ini menggunakan 5 sample data E-Seal untuk development:
- **Perusahaan A, B, C, D** dengan berbagai brand dan model
- **Status**: Locked/Unlocked
- **Jarak Tempuh**: Berbagai kategori (<5 Km, 10-50 Km, >500 Km, dll)
- **Search**: Dapat mencari berdasarkan semua field (perusahaan, vendor ID, merk, model, IMEI, dll)

## 🎯 Roadmap

### Phase 1 (Current)
- ✅ Basic E-Seal data management UI
- ✅ Responsive design dengan Tailwind CSS
- ✅ Type-safe API dengan mock data
- ✅ Pagination dan search functionality
- ✅ Netlify deployment setup

### Phase 2 (Upcoming)
- 🔄 Complete CRUD operations (POST, PUT, DELETE)
- 🔄 Database integration (replace mock data)
- 🔄 User authentication system
- 🔄 Real-time tracking integration

### Phase 3 (Future)
- 📋 Mobile app development
- 📋 Advanced analytics dashboard
- 📋 Notification system
- 📋 Export/Import functionality

## �🔐 Security Features

- **Type-safe API**: End-to-end type safety
- **CORS Protection**: Configured cross-origin policies
- **Role-based Access**: Multi-level user permissions
- **Input Validation**: Client dan server-side validation

## 🤝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request


**Live Demo**: [https://eseal-monitor.netlify.app](https://eseal-monitor.netlify.app)

---

⭐ **Star this repo if you find it helpful!**
