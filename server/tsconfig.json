{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    // Environment settings
    "lib": ["ESNext"],
    "target": "ESNext",
    "module": "ESNext",
    "jsx": "react-jsx",
    "jsxImportSource": "hono/jsx",

    // Types
    "types": ["bun-types"],

    // Output settings - flat structure
    "declaration": false,  // Disable .d.ts generation for dev
    "outDir": "dist",
    "noEmit": false,  // Enable file emission
    "emitDecoratorMetadata": true,
    "skipLibCheck": true,

    // Module resolution
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": false,

    // Path resolution
    "baseUrl": "../",
    "paths": {
      "shared": ["./shared/dist"],
      "shared/*": ["./shared/dist/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
