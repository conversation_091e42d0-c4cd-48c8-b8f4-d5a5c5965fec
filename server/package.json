{"name": "server", "version": "0.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "prisma generate && tsc", "dev": "bun --watch run src/index.ts && tsc --watch"}, "dependencies": {"@prisma/client": "^6.12.0", "@types/node-cron": "^3.0.11", "better-auth": "^1.3.4", "hono": "^4.7.11", "node-cron": "^4.2.1", "prisma": "^6.12.0", "shared": "workspace:*"}, "devDependencies": {"@types/bun": "latest"}}