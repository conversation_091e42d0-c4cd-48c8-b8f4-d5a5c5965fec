-- CreateTable
CREATE TABLE "beacukai_api_log" (
    "id" TEXT NOT NULL,
    "endpoint" TEXT NOT NULL,
    "method" TEXT NOT NULL,
    "requestUrl" TEXT NOT NULL,
    "requestBody" JSONB,
    "requestHeaders" JSONB,
    "responseStatus" INTEGER NOT NULL,
    "responseBody" JSONB,
    "responseHeaders" JSONB,
    "duration" INTEGER,
    "success" BOOLEAN NOT NULL,
    "errorMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "beacukai_api_log_pkey" PRIMARY KEY ("id")
);
