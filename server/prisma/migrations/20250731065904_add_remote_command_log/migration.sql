-- CreateTable
CREATE TABLE "remote_command_log" (
    "id" TEXT NOT NULL,
    "esealId" TEXT NOT NULL,
    "gpsDeviceId" TEXT NOT NULL,
    "commandType" INTEGER NOT NULL,
    "commandId" TEXT NOT NULL,
    "second" INTEGER,
    "speed" INTEGER,
    "status" TEXT NOT NULL,
    "result" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "remote_command_log_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "remote_command_log" ADD CONSTRAINT "remote_command_log_esealId_fkey" FOREIGN KEY ("esealId") REFERENCES "eseal"("id") ON DELETE CASCADE ON UPDATE CASCADE;
