/*
  Warnings:

  - You are about to drop the `roles` table. If the table is not empty, all the data it contains will be lost.

*/
-- <PERSON><PERSON>Enum
CREATE TYPE "public"."ModuleType" AS ENUM ('DATA_ESEAL', 'TRACKING_DATA', 'LOGS', 'PENGATURAN_SISTEM');

-- AlterTable
ALTER TABLE "public"."member" ADD COLUMN     "customRoleId" TEXT;

-- DropTable
DROP TABLE "public"."roles";

-- CreateTable
CREATE TABLE "public"."custom_role" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "organizationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "custom_role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."custom_permission" (
    "id" TEXT NOT NULL,
    "module" "public"."ModuleType" NOT NULL,
    "canRead" BOOLEAN NOT NULL DEFAULT false,
    "canCreate" BOOLEAN NOT NULL DEFAULT false,
    "canUpdate" BOOLEAN NOT NULL DEFAULT false,
    "canDelete" BOOLEAN NOT NULL DEFAULT false,
    "customRoleId" TEXT NOT NULL,

    CONSTRAINT "custom_permission_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "public"."member" ADD CONSTRAINT "member_customRoleId_fkey" FOREIGN KEY ("customRoleId") REFERENCES "public"."custom_role"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."custom_role" ADD CONSTRAINT "custom_role_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."custom_permission" ADD CONSTRAINT "custom_permission_customRoleId_fkey" FOREIGN KEY ("customRoleId") REFERENCES "public"."custom_role"("id") ON DELETE CASCADE ON UPDATE CASCADE;
