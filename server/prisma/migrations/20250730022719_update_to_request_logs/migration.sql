/*
  Warnings:

  - You are about to drop the `beacukai_api_log` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE "beacukai_api_log";

-- CreateTable
CREATE TABLE "request_logs" (
    "id" TEXT NOT NULL,
    "requestBody" JSONB,
    "requestHeader" JSONB,
    "requestUrl" TEXT NOT NULL,
    "status" INTEGER NOT NULL,
    "responseBody" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "request_logs_pkey" PRIMARY KEY ("id")
);
