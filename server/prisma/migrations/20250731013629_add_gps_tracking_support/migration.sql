-- AlterTable
ALTER TABLE "tracking_log" ADD COLUMN     "beacukaiStatus" TEXT,
ADD COLUMN     "direction" TEXT,
ADD COLUMN     "gpsDeviceId" TEXT,
ADD COLUMN     "gpsTime" TEXT,
ADD COLUMN     "locate" INTEGER,
ADD COLUMN     "mileage" TEXT,
ADD COLUMN     "online" BOOLEAN;

-- CreateTable
CREATE TABLE "tracking_session" (
    "id" TEXT NOT NULL,
    "esealId" TEXT NOT NULL,
    "sessionStatus" TEXT NOT NULL DEFAULT 'INACTIVE',
    "startedAt" TIMESTAMP(3),
    "stoppedAt" TIMESTAMP(3),
    "beacukaiStartStatus" TEXT,
    "beacukaiStopStatus" TEXT,
    "totalUpdates" INTEGER NOT NULL DEFAULT 0,
    "lastUpdateAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tracking_session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "device_status" (
    "id" TEXT NOT NULL,
    "esealId" TEXT NOT NULL,
    "currentStatus" TEXT NOT NULL DEFAULT 'INACTIVE',
    "lastKnownLat" TEXT,
    "lastKnownLng" TEXT,
    "lastKnownAddress" TEXT,
    "gpsOnline" BOOLEAN NOT NULL DEFAULT false,
    "lastGpsUpdate" TIMESTAMP(3),
    "batteryLevel" TEXT,
    "signalStrength" TEXT,
    "registeredWithBeacukai" BOOLEAN NOT NULL DEFAULT false,
    "beacukaiDeviceId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "device_status_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cron_job_log" (
    "id" TEXT NOT NULL,
    "jobName" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "message" TEXT,
    "executionTime" INTEGER,
    "devicesProcessed" INTEGER,
    "errorsCount" INTEGER,
    "jobData" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "cron_job_log_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "device_status_esealId_key" ON "device_status"("esealId");

-- AddForeignKey
ALTER TABLE "tracking_session" ADD CONSTRAINT "tracking_session_esealId_fkey" FOREIGN KEY ("esealId") REFERENCES "eseal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "device_status" ADD CONSTRAINT "device_status_esealId_fkey" FOREIGN KEY ("esealId") REFERENCES "eseal"("id") ON DELETE CASCADE ON UPDATE CASCADE;
