-- CreateTable
CREATE TABLE "user" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "emailVerified" BOOLEAN NOT NULL,
    "image" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "session" (
    "id" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "token" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "userId" TEXT NOT NULL,

    CONSTRAINT "session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "account" (
    "id" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "accessToken" TEXT,
    "refreshToken" TEXT,
    "idToken" TEXT,
    "accessTokenExpiresAt" TIMESTAMP(3),
    "refreshTokenExpiresAt" TIMESTAMP(3),
    "scope" TEXT,
    "password" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "verification" (
    "id" TEXT NOT NULL,
    "identifier" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3),
    "updatedAt" TIMESTAMP(3),

    CONSTRAINT "verification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "eseal" (
    "id" TEXT NOT NULL,
    "noEseal" TEXT NOT NULL,
    "noImei" TEXT NOT NULL,
    "idVendor" TEXT NOT NULL,
    "merk" TEXT,
    "model" TEXT,
    "tipe" TEXT,
    "status" TEXT NOT NULL DEFAULT 'INACTIVE',
    "alamatAsal" TEXT NOT NULL,
    "alamatTujuan" TEXT NOT NULL,
    "lokasiAsal" TEXT NOT NULL,
    "lokasiTujuan" TEXT NOT NULL,
    "latitudeAsal" TEXT NOT NULL,
    "longitudeAsal" TEXT NOT NULL,
    "latitudeTujuan" TEXT NOT NULL,
    "longitudeTujuan" TEXT NOT NULL,
    "noPolisi" TEXT NOT NULL,
    "ukKontainer" TEXT NOT NULL,
    "jnsKontainer" TEXT NOT NULL,
    "noKontainer" TEXT NOT NULL,
    "namaDriver" TEXT NOT NULL,
    "nomorTeleponDriver" TEXT NOT NULL,
    "nomorAJU" TEXT,
    "statusValidasi" TEXT NOT NULL DEFAULT 'PENDING',
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "eseal_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "eseal_dokumen" (
    "id" TEXT NOT NULL,
    "esealId" TEXT NOT NULL,
    "jenisMuat" TEXT NOT NULL,
    "jumlahKontainer" TEXT NOT NULL,
    "kodeDokumen" TEXT NOT NULL,
    "kodeKantor" TEXT NOT NULL,
    "nomorAju" TEXT NOT NULL,
    "nomorDokumen" TEXT NOT NULL,
    "tanggalDokumen" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "eseal_dokumen_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tracking_log" (
    "id" TEXT NOT NULL,
    "esealId" TEXT NOT NULL,
    "latitude" TEXT,
    "longitude" TEXT,
    "address" TEXT,
    "altitude" TEXT,
    "speed" TEXT,
    "battery" TEXT,
    "dayaAki" TEXT,
    "event" TEXT,
    "kota" TEXT,
    "provinsi" TEXT,
    "apiResponse" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tracking_log_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "beacukai_token" (
    "id" TEXT NOT NULL,
    "environment" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "beacukai_token_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_email_key" ON "user"("email");

-- CreateIndex
CREATE UNIQUE INDEX "session_token_key" ON "session"("token");

-- CreateIndex
CREATE UNIQUE INDEX "eseal_noEseal_key" ON "eseal"("noEseal");

-- CreateIndex
CREATE UNIQUE INDEX "eseal_noImei_key" ON "eseal"("noImei");

-- CreateIndex
CREATE UNIQUE INDEX "beacukai_token_environment_key" ON "beacukai_token"("environment");

-- AddForeignKey
ALTER TABLE "session" ADD CONSTRAINT "session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "account" ADD CONSTRAINT "account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "eseal" ADD CONSTRAINT "eseal_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "eseal_dokumen" ADD CONSTRAINT "eseal_dokumen_esealId_fkey" FOREIGN KEY ("esealId") REFERENCES "eseal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tracking_log" ADD CONSTRAINT "tracking_log_esealId_fkey" FOREIGN KEY ("esealId") REFERENCES "eseal"("id") ON DELETE CASCADE ON UPDATE CASCADE;
