// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-1.1.x", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id
  name          String
  email         String    @unique
  emailVerified Boolean
  image         String?
  createdAt     DateTime
  updatedAt     DateTime
  sessions      Session[]
  accounts      Account[]
  eseals        ESeal[]

  // Admin Plugin Fields
  role       String?
  banned     Boolean?
  banReason  String?
  banExpires DateTime?

  // Organization Plugin Relations
  memberships  Member[]

  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String   @unique
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Admin Plugin Fields
  impersonatedBy String?

  // Organization Plugin Fields
  activeOrganizationId String?
  activeOrganization   Organization? @relation("ActiveOrganization", fields: [activeOrganizationId], references: [id], onDelete: SetNull)

  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

// E-Seal Models

model ESeal {
  id       String  @id @default(cuid())
  // Basic E-Seal Info
  noEseal  String  @unique
  noImei   String  @unique
  idVendor String
  merk     String?
  model    String?
  tipe     String?
  status   String  @default("INACTIVE") // ACTIVE, INACTIVE, LOCKED, UNLOCKED

  // Location Info
  alamatAsal      String
  alamatTujuan    String
  lokasiAsal      String
  lokasiTujuan    String
  latitudeAsal    String
  longitudeAsal   String
  latitudeTujuan  String
  longitudeTujuan String

  // Vehicle & Driver Info
  noPolisi           String
  ukKontainer        String
  jnsKontainer       String
  noKontainer        String
  namaDriver         String
  nomorTeleponDriver String

  // AJU & Document Info
  nomorAJU       String?
  statusValidasi String  @default("PENDING") // PENDING, VALIDATING, VALID, INVALID

  // GPS Integration
  gpsDeviceId String? // Device ID from GPS API for tracking

  // Beacukai Sync Status
  isSync              Boolean @default(false) // Sync status with Beacukai API
  beacukaiResponseLog Json? // Response from Beacukai API
  beacukaiId          String? // ID from Beacukai API response
  idEseal             String? // Real E-Seal ID from Beacukai API response

  // Organization relation
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: SetNull)

  // Relations
  userId           String
  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  dokumen          ESealDokumen[]
  trackingLogs     TrackingLog[]
  trackingSessions TrackingSession[]
  deviceStatus     DeviceStatus?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("eseal")
}

model ESealDokumen {
  id      String @id @default(cuid())
  esealId String
  eseal   ESeal  @relation(fields: [esealId], references: [id], onDelete: Cascade)

  jenisMuat       String
  jumlahKontainer String
  kodeDokumen     String
  kodeKantor      String
  nomorAju        String
  nomorDokumen    String
  tanggalDokumen  String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("eseal_dokumen")
}

model TrackingLog {
  id      String @id @default(cuid())
  esealId String
  eseal   ESeal  @relation(fields: [esealId], references: [id], onDelete: Cascade)

  // Location Data
  latitude  String?
  longitude String?
  address   String?
  altitude  String?
  speed     String?

  // Device Data
  battery  String?
  dayaAki  String?
  event    String?
  kota     String?
  provinsi String?

  // GPS API Data
  gpsDeviceId String? // Device ID from GPS API
  gpsTime     String? // GPS timestamp
  direction   String? // Direction from GPS
  mileage     String? // Mileage from GPS
  online      Boolean? // Online status from GPS
  locate      Int? // Location accuracy from GPS

  // Beacukai API Response Data
  apiResponse    Json?
  beacukaiStatus String? // Response status from Beacukai API

  createdAt DateTime @default(now())

  @@map("tracking_log")
}

model RequestLog {
  id            String   @id @default(cuid())
  requestBody   Json? // Request body (jika ada)
  requestHeader Json? // Request headers
  requestUrl    String // Full URL yang di-hit
  status        Int // HTTP status code
  responseBody  Json? // Response body
  isSync        Boolean  @default(false) // Sync status with external API
  createdAt     DateTime @default(now())

  @@map("request_logs")
}

model TrackingSession {
  id      String @id @default(cuid())
  esealId String
  eseal   ESeal  @relation(fields: [esealId], references: [id], onDelete: Cascade)

  // Session Info
  sessionStatus String    @default("INACTIVE") // INACTIVE, ACTIVE, STOPPED, ERROR
  startedAt     DateTime?
  stoppedAt     DateTime?

  // Beacukai API Status
  beacukaiStartStatus String? // Response from tracking/start
  beacukaiStopStatus  String? // Response from tracking/stop

  // Position Update Stats
  totalUpdates Int       @default(0)
  lastUpdateAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("tracking_session")
}

model DeviceStatus {
  id      String @id @default(cuid())
  esealId String @unique
  eseal   ESeal  @relation(fields: [esealId], references: [id], onDelete: Cascade)

  // Current Status
  currentStatus    String  @default("INACTIVE") // INACTIVE, ACTIVE, LOCKED, UNLOCKED
  lastKnownLat     String?
  lastKnownLng     String?
  lastKnownAddress String?

  // GPS Status
  gpsOnline     Boolean   @default(false)
  lastGpsUpdate DateTime?

  // Battery & Health
  batteryLevel   String?
  signalStrength String?

  // Beacukai Registration
  registeredWithBeacukai Boolean @default(false)
  beacukaiDeviceId       String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("device_status")
}

model CronJobLog {
  id               String  @id @default(cuid())
  jobName          String // e.g., "position-update", "device-sync"
  status           String // SUCCESS, ERROR, RUNNING
  message          String?
  executionTime    Int? // Execution time in milliseconds
  devicesProcessed Int?
  errorsCount      Int?
  jobData          Json?

  createdAt DateTime @default(now())

  @@map("cron_job_log")
}

// Organization Plugin Models

model Organization {
  id        String    @id @default(cuid())
  name      String
  slug      String    @unique
  logo      String?
  metadata  Json?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @default(now()) @updatedAt
  members     Member[]
  invites     Invitation[]
  sessions    Session[]    @relation("ActiveOrganization")
  customRoles CustomRole[]
  eseals      ESeal[]

  @@map("organization")
}

model Member {
  id             String       @id @default(cuid())
  userId         String
  organizationId String
  role           String // This can be a default role like 'admin', 'member'
  customRoleId   String?      // Link to the new custom role
  customRole     CustomRole?  @relation(fields: [customRoleId], references: [id])
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @default(now()) @updatedAt
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  invites        Invitation[]

  @@unique([userId, organizationId])
  @@map("member")
}

model Invitation {
  id             String       @id @default(cuid())
  email          String
  inviterId      String
  organizationId String
  role           String
  status         String       @default("PENDING") // PENDING, ACCEPTED, REJECTED, CANCELED
  expiresAt      DateTime
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @default(now()) @updatedAt
  inviter        Member       @relation(fields: [inviterId], references: [id], onDelete: Cascade)
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)


  @@map("invitation")
}

enum ModuleType {
  DATA_ESEAL
  TRACKING_DATA
  LOGS
  PENGATURAN_SISTEM
}

model CustomRole {
  id             String             @id @default(cuid())
  name           String
  description    String?
  organization   Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  permissions    CustomPermission[]
  members        Member[]
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @updatedAt

  @@map("custom_role")
}

model CustomPermission {
  id           String     @id @default(cuid())
  module       ModuleType
  canRead      Boolean    @default(false)
  canCreate    Boolean    @default(false)
  canUpdate    Boolean    @default(false)
  canDelete    Boolean    @default(false)
  customRole   CustomRole @relation(fields: [customRoleId], references: [id], onDelete: Cascade)
  customRoleId String

  @@map("custom_permission")
}
