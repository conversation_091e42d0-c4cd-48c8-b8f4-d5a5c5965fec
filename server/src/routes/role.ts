import { Hono } from 'hono';
import { PrismaClient, ModuleType } from '@prisma/client';

const prisma = new PrismaClient();

export const role = new Hono()
  // Get all custom roles for superadmin
  .get('/all', async (c) => {
    const roles = await prisma.customRole.findMany({
      include: { 
        permissions: true,
        organization: {
          select: {
            name: true
          }
        }
      },
    });

    const formattedRoles = roles.map(role => ({
      ...role,
      organizationName: role.organization.name
    }));

    return c.json({ success: true, data: formattedRoles });
  })

  // Get all custom roles for an organization
  .get('/org/:orgId', async (c) => {
    const { orgId } = c.req.param();
    const roles = await prisma.customRole.findMany({
      where: { organizationId: orgId },
      include: { permissions: true },
    });

    console.log('=== FETCH CUSTOM ROLES ===');
    console.log(`Found ${roles.length} custom roles for organization ${orgId}`);
    roles.forEach((role, index) => {
      console.log(`Custom Role ${index + 1}:`, {
        id: role.id,
        name: role.name,
        description: role.description,
        permissionsCount: role.permissions.length
      });
      role.permissions.forEach((perm, permIndex) => {
        console.log(`  Permission ${permIndex + 1}:`, {
          module: perm.module,
          canCreate: perm.canCreate,
          canRead: perm.canRead,
          canUpdate: perm.canUpdate,
          canDelete: perm.canDelete
        });
      });
    });
    
    return c.json({ success: true, data: roles });
  })

  // Check and update user role to superadmin (for development)
  .post('/make-superadmin/:userId', async (c) => {
    const { userId } = c.req.param();
    
    try {
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: { role: 'superadmin' }
      });
      
      console.log('User updated to superadmin:', updatedUser);
      return c.json({ success: true, data: updatedUser });
    } catch (error) {
      console.error('Error updating user role:', error);
      return c.json({ success: false, error: 'Failed to update user role' }, 500);
    }
  })

  // Get current user info
  .get('/current-user/:userId', async (c) => {
    const { userId } = c.req.param();
    
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, name: true, email: true, role: true }
      });
      
      console.log('Current user info:', user);
      return c.json({ success: true, data: user });
    } catch (error) {
      console.error('Error fetching user info:', error);
      return c.json({ success: false, error: 'Failed to fetch user info' }, 500);
    }
  })

  // Get all member roles from all organizations (for superadmin)
  .get('/members/all', async (c) => {
    console.log('=== FETCH ALL MEMBER ROLES (SUPERADMIN) ===');
    
    try {
      const members = await prisma.member.findMany({
        include: {
          user: { select: { id: true, name: true, email: true, role: true } },
          customRole: { include: { permissions: true } },
          organization: { select: { id: true, name: true } }
        }
      });
      
      console.log(`Found ${members.length} members across all organizations`);
      
      // Extract unique roles from members
      const roleMap = new Map();
      
      members.forEach((member, index) => {
        console.log(`Member ${index + 1}:`, {
          userId: member.userId,
          userRole: member.user.role,
          organizationId: member.organizationId,
          organizationName: member.organization.name,
          customRoleId: member.customRoleId
        });
        
        let roleData;
        
        if (member.customRole) {
          // Custom role from CustomRole table
          roleData = {
            id: member.customRole.id,
            name: member.customRole.name,
            description: member.customRole.description,
            permissions: member.customRole.permissions.map(perm => ({
              id: perm.id,
              module: perm.module,
              canCreate: perm.canCreate,
              canRead: perm.canRead,
              canUpdate: perm.canUpdate,
              canDelete: perm.canDelete
            })),
            isDefault: false,
            organizationId: member.organizationId,
            organizationName: member.organization.name
          };
        } else if (member.user.role && member.user.role !== 'superadmin') {
          // Default role from User table (admin, member, etc.)
          const defaultPermissions = member.user.role === 'admin' 
            ? Object.values(ModuleType).map(module => ({
                module,
                canCreate: module !== ModuleType.PENGATURAN_SISTEM,
                canRead: true,
                canUpdate: module !== ModuleType.PENGATURAN_SISTEM,
                canDelete: module !== ModuleType.PENGATURAN_SISTEM
              }))
            : Object.values(ModuleType).map(module => ({
                module,
                canCreate: false,
                canRead: true,
                canUpdate: false,
                canDelete: false
              }));
              
          roleData = {
            id: `${member.user.role}-${member.organizationId}`,
            name: member.user.role === 'admin' ? 'Admin' : 'Member',
            description: member.user.role === 'admin' 
              ? 'Admin role with limited permissions'
              : 'Member role with read-only permissions',
            permissions: defaultPermissions,
            isDefault: true,
            organizationId: member.organizationId,
            organizationName: member.organization.name
          };
        }
        
        if (roleData && !roleMap.has(roleData.id)) {
          roleMap.set(roleData.id, roleData);
        }
      });
      
      const uniqueRoles = Array.from(roleMap.values());
      console.log(`Extracted ${uniqueRoles.length} unique roles`);
      
      return c.json({ success: true, data: uniqueRoles });
    } catch (error) {
      console.error('Error fetching all member roles:', error);
      return c.json({ success: false, error: 'Failed to fetch all member roles' }, 500);
    }
  })

  // Get all member roles for an organization (from Member table)
  .get('/members/:orgId', async (c) => {
    const { orgId } = c.req.param();
    
    console.log('=== FETCH MEMBER ROLES ===');
    console.log(`Fetching member roles for organization: ${orgId}`);
    
    try {
      const members = await prisma.member.findMany({
        where: { organizationId: orgId },
        include: {
          user: { select: { id: true, name: true, email: true, role: true } },
          customRole: { include: { permissions: true } }
        },
      });
      
      console.log(`Found ${members.length} members in organization ${orgId}`);
      members.forEach((member, index) => {
        console.log(`Member ${index + 1}:`, {
          userId: member.user.id,
          userName: member.user.name,
          userEmail: member.user.email,
          defaultRole: member.user.role,
          memberRole: member.role,
          customRoleId: member.customRole?.id,
          customRoleName: member.customRole?.name
        });
      });
      
      // Extract unique roles from members
      const memberRoles = members.reduce((acc: any[], member) => {
        // Add default role if it exists
        if (member.role && !acc.find(r => r.id === member.role)) {
          acc.push({
            id: member.role,
            name: member.role.charAt(0).toUpperCase() + member.role.slice(1),
            description: `Default ${member.role} role`,
            permissions: [], // Default roles don't have custom permissions
            isDefault: true,
            memberCount: members.filter(m => m.role === member.role).length
          });
        }
        
        // Add custom role if it exists
        if (member.customRole && !acc.find(r => r.id === member.customRole!.id)) {
          acc.push({
            ...member.customRole,
            memberCount: members.filter(m => m.customRoleId === member.customRole!.id).length
          });
        }
        
        return acc;
      }, []);
      
      console.log(`Extracted ${memberRoles.length} unique roles from members:`);
      memberRoles.forEach((role, index) => {
        console.log(`Role ${index + 1}:`, {
          id: role.id,
          name: role.name,
          isDefault: role.isDefault || false,
          memberCount: role.memberCount,
          permissionsCount: role.permissions.length
        });
        if (role.permissions.length > 0) {
           role.permissions.forEach((perm: any, permIndex: number) => {
             console.log(`  Permission ${permIndex + 1}:`, {
               module: perm.module,
               canCreate: perm.canCreate,
               canRead: perm.canRead,
               canUpdate: perm.canUpdate,
               canDelete: perm.canDelete
             });
           });
         }
      });
      
      return c.json({ success: true, data: memberRoles });
    } catch (error) {
      console.error('❌ Error fetching member roles:', error);
      return c.json({ success: false, error: 'Failed to fetch member roles' }, 500);
    }
  })

  // Create a new custom role
  .post('/', async (c) => {
    const { name, description, organizationId, permissions } = await c.req.json();

    if (!name || !organizationId || !permissions) {
      return c.json({ success: false, error: 'Missing required fields' }, 400);
    }

    console.log('=== CREATE ROLE REQUEST ===');
    console.log('Received data for new role:', { name, description, organizationId, permissions });
    console.log('Permissions array length:', permissions.length);
    permissions.forEach((p: any, index: number) => {
      console.log(`Permission ${index + 1}:`, {
        module: p.module,
        canCreate: p.canCreate,
        canRead: p.canRead,
        canUpdate: p.canUpdate,
        canDelete: p.canDelete
      });
    });

    try {
      const newRole = await prisma.customRole.create({
        data: {
          name,
          description,
          organizationId,
          permissions: {
            create: permissions.map((p: any) => ({
              module: p.module,
              canRead: p.canRead,
              canCreate: p.canCreate,
              canUpdate: p.canUpdate,
              canDelete: p.canDelete,
            })),
          },
        },
        include: { permissions: true },
      });

      console.log('=== ROLE CREATED SUCCESSFULLY ===');
      console.log('Created role ID:', newRole.id);
      console.log('Created role name:', newRole.name);
      console.log('Created permissions count:', newRole.permissions.length);
      newRole.permissions.forEach((perm, index) => {
        console.log(`Saved Permission ${index + 1}:`, {
          id: perm.id,
          module: perm.module,
          canCreate: perm.canCreate,
          canRead: perm.canRead,
          canUpdate: perm.canUpdate,
          canDelete: perm.canDelete
        });
      });
      
      return c.json({ success: true, data: newRole }, 201);
    } catch (error) {
      console.error('❌ Error creating role:', error);
      return c.json({ success: false, error: 'Failed to create role' }, 500);
    }
  })

  // Get a specific role by ID
  .get('/:id', async (c) => {
    const { id } = c.req.param();
    const role = await prisma.customRole.findUnique({
      where: { id },
      include: { permissions: true },
    });
    if (!role) {
      return c.json({ success: false, error: 'Role not found' }, 404);
    }
    return c.json({ success: true, data: role });
  })

  // Update a custom role
  .put('/:id', async (c) => {
    const { id } = c.req.param();
    const { name, description, permissions } = await c.req.json();

    if (!name || !permissions) {
      return c.json({ success: false, error: 'Missing required fields' }, 400);
    }

    console.log('=== UPDATE ROLE REQUEST ===');
    console.log(`Updating role ${id} with data:`, { name, description, permissions });
    console.log('Permissions array length:', permissions.length);
    permissions.forEach((p: any, index: number) => {
      console.log(`Permission ${index + 1}:`, {
        module: p.module,
        canCreate: p.canCreate,
        canRead: p.canRead,
        canUpdate: p.canUpdate,
        canDelete: p.canDelete
      });
    });

    // Check if this is a default role (admin-xxx, member-xxx, etc.)
    const isDefaultRole = id.startsWith('admin-') || id.startsWith('member-') || id.startsWith('Super Admin-') || id.startsWith('Admin-') || id.startsWith('Member-');
    
    if (isDefaultRole) {
      console.log(`⚠️ Attempted to update default role ${id}. Default roles cannot be modified.`);
      // Return success response for default roles to prevent frontend errors
      // but don't actually update anything since default roles are not stored in database
      const mockRole = {
        id,
        name,
        description,
        permissions: permissions.map((p: any, index: number) => ({
          id: `mock-perm-${index}`,
          module: p.module,
          canRead: p.canRead,
          canCreate: p.canCreate,
          canUpdate: p.canUpdate,
          canDelete: p.canDelete,
          customRoleId: id
        })),
        organizationId: 'default',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      console.log('=== DEFAULT ROLE UPDATE SIMULATED ===');
      console.log('Note: Default roles are not actually updated in database');
      return c.json({ success: true, data: mockRole });
    }

    try {
      // Transaction to update role and permissions
      const updatedRole = await prisma.$transaction(async (prisma) => {
        console.log(`Deleting existing permissions for role ${id}`);
        await prisma.customPermission.deleteMany({
          where: { customRoleId: id },
        });

        console.log(`Creating new permissions for role ${id}`);
        const role = await prisma.customRole.update({
          where: { id },
          data: {
            name,
            description,
            permissions: {
              create: permissions.map((p: any) => ({
                module: p.module,
                canRead: p.canRead,
                canCreate: p.canCreate,
                canUpdate: p.canUpdate,
                canDelete: p.canDelete,
              })),
            },
          },
          include: { permissions: true },
        });
        return role;
      });

      console.log('=== ROLE UPDATED SUCCESSFULLY ===');
      console.log('Updated role ID:', updatedRole.id);
      console.log('Updated role name:', updatedRole.name);
      console.log('Updated permissions count:', updatedRole.permissions.length);
      updatedRole.permissions.forEach((perm, index) => {
        console.log(`Updated Permission ${index + 1}:`, {
          id: perm.id,
          module: perm.module,
          canCreate: perm.canCreate,
          canRead: perm.canRead,
          canUpdate: perm.canUpdate,
          canDelete: perm.canDelete
        });
      });
      
      return c.json({ success: true, data: updatedRole });
    } catch (error) {
      console.error('❌ Error updating role:', error);
      return c.json({ success: false, error: 'Failed to update role' }, 500);
    }
  })

  // Delete a custom role
  .delete('/org/:orgId/:roleId', async (c) => {
    const { orgId, roleId } = c.req.param();
    console.log(`Attempting to delete role with id: ${roleId} from organization: ${orgId}`);
    
    try {
      // Verify the role belongs to the organization
      const role = await prisma.customRole.findFirst({
        where: { 
          id: roleId,
          organizationId: orgId 
        },
      });
      
      if (!role) {
        console.log(`Role ${roleId} not found in organization ${orgId}`);
        return c.json({ success: false, message: 'Role not found' }, 404);
      }
      
      // Delete the role
      await prisma.customRole.delete({
        where: { id: roleId },
      });
      
      console.log(`Successfully deleted role with id: ${roleId}`);
      return c.json({ success: true, message: 'Role deleted successfully' });
    } catch (error) {
      console.error(`Failed to delete role with id: ${roleId}`, error);
      return c.json({ success: false, message: 'Failed to delete role' }, 500);
    }
  })
  
  .delete('/:id', async (c) => {
    const { id } = c.req.param();
    console.log(`Attempting to delete role with id: ${id}`);
    try {
      await prisma.customRole.delete({
        where: { id },
      });
      console.log(`Successfully deleted role with id: ${id}`);
      return c.json({ success: true });
    } catch (error) {
      console.error(`Failed to delete role with id: ${id}`, error);
      return c.json({ success: false, error: 'Role not found' }, 404);
    }
  });
