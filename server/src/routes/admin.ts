import { Hono } from 'hono';
import type { Next } from 'hono';
import { PrismaClient } from '@prisma/client';
import { auth } from '../auth';

const prisma = new PrismaClient();
export const adminRoutes = new Hono();

// Middleware to check for superadmin role
const superadminAuth = async (c: any, next: Next) => {
  const session = await auth.api.getSession(c.req.raw);
  if (!session || !session.user || !session.user.role.includes('superadmin')) {
    return c.json({ success: false, error: 'Unauthorized' }, 403);
  }
  await next();
};

adminRoutes.use('*', superadminAuth);

adminRoutes.get('/users', async (c: any) => {
  try {
    const users = await prisma.user.findMany({
      include: {
        memberships: {
          include: {
            organization: true
          }
        },
        sessions: {
          orderBy: {
            updatedAt: 'desc'
          },
          take: 1
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return c.json({
      success: true,
      data: users
    });
  } catch (error: any) {
    console.error('Failed to fetch users:', error.message, error.stack);
    return c.json({ success: false, error: `Failed to fetch users: ${error.message}` }, 500);
  }
});

adminRoutes.get('/organizations', async (c: any) => {
  const { page = '1', limit = '10', search = '' } = c.req.query();
  const pageNum = parseInt(page, 10);
  const limitNum = parseInt(limit, 10);
  const offset = (pageNum - 1) * limitNum;

  try {
    const where = search ? {
      OR: [
        { name: { contains: search } },
        { slug: { contains: search } },
      ],
    } : {};

    const organizations = await prisma.organization.findMany({
      where,
      take: limitNum,
      skip: offset,
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        members: true, // Include members to get the count
      },
    });

    const total = await prisma.organization.count({ where });

    return c.json({
      success: true,
      data: organizations,
      total,
      page: pageNum,
      limit: limitNum,
    });
  } catch (error: any) {
    console.error('Failed to fetch organizations:', error.message, error.stack);
    return c.json({ success: false, error: `Failed to fetch organizations: ${error.message}` }, 500);
  }
});
