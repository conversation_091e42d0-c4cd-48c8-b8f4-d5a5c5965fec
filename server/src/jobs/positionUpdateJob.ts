import cron from 'node-cron';
import { PrismaClient } from '@prisma/client';
import { CronJobService } from '../services/CronJobService';

const prisma = new PrismaClient();
const cronJobService = new CronJobService(prisma);

/**
 * Position Update Cron Job
 * Runs every 5 minutes to update GPS positions for all E-Seals
 */
export function startPositionUpdateJob() {
  console.log('🚀 Starting position update cron job scheduler...');

  // Run every 5 minutes: '*/5 * * * *'
  const schedule = '*/5 * * * *';

  cron.schedule(schedule, async () => {
    console.log(`⏰ Position update cron job triggered at ${new Date().toISOString()}`);

    try {
      // Run the actual position update job
      await cronJobService.runPositionUpdateJob();
      console.log(`✅ Position update cron job completed successfully`);
    } catch (error) {
      console.error('💥 Position update cron job failed:', error);
    }
  }, {
    timezone: "Asia/Jakarta"
  });

  console.log(`✅ Position update cron job scheduled: ${schedule}`);
}

/**
 * Cleanup Job - runs daily at midnight to clean old logs
 */
export function startCleanupJob() {
  console.log('🧹 Starting cleanup cron job scheduler...');

  // Run daily at midnight: '0 0 * * *'
  cron.schedule('0 0 * * *', async () => {
    console.log(`🧹 Cleanup job triggered at ${new Date().toISOString()}`);

    try {
      // Keep only last 30 days of logs
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const deletedCount = await prisma.cronJobLog.deleteMany({
        where: {
          createdAt: {
            lt: thirtyDaysAgo
          }
        }
      });

      console.log(`🧹 Cleaned up ${deletedCount.count} old cron job logs`);
    } catch (error) {
      console.error('💥 Cleanup job failed:', error);
    }
  }, {
    timezone: "Asia/Jakarta"
  });

  console.log('✅ Cleanup cron job scheduled: daily at midnight');
}

/**
 * Start all cron jobs
 */
export function startAllCronJobs() {
  console.log('🎯 Initializing all cron jobs...');

  startPositionUpdateJob();
  startCleanupJob();

  console.log('✅ All cron jobs initialized successfully');
}

/**
 * Manual trigger for testing
 */
export async function triggerPositionUpdateManually() {
  console.log('🔧 Manually triggering position update job...');

  try {
    await cronJobService.runPositionUpdateJob();
    console.log(`✅ Manual position update job completed successfully`);
    return { success: true, message: 'Position update completed' };
  } catch (error) {
    console.error('❌ Manual position update job failed:', error);
    throw error;
  }
}

// Note: Signal handlers moved to main server file to avoid duplicates
