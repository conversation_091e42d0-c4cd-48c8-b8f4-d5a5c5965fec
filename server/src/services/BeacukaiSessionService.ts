import { PrismaClient } from '@prisma/client';

export interface BeacukaiSession {
  sessionId: string;
  cookies: string[];
  expiresAt: Date;
  isValid: boolean;
}

export class BeacukaiSessionService {
  private prisma: PrismaClient;
  private baseUrl: string;
  private credentials: {
    username: string;
    password: string;
    appId: string;
  };
  private currentSession: BeacukaiSession | null = null;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;

    // Use the same config as BeacukaiApiService
    const environment = (process.env.BEACUKAI_ENVIRONMENT || 'development') as 'development' | 'production';
    const isDev = environment === 'development';

    this.baseUrl = isDev ?
      (process.env.BEACUKAI_DEV_BASE_URL || 'https://apisdev-gw.beacukai.go.id') :
      (process.env.BEACUKAI_PROD_BASE_URL || 'https://apis-gw.beacukai.go.id');

    // Handle password with special characters more robustly
    // Support both plain text and base64 encoded passwords
    let devPassword = process.env.BEACUKAI_DEV_PASSWORD || 'aR7#pL9qZ@1m';
    let prodPassword = process.env.BEACUKAI_PROD_PASSWORD || '';

    // Check if password is base64 encoded (for EasyPanel compatibility)
    const devPasswordBase64 = process.env.BEACUKAI_DEV_PASSWORD_BASE64;
    const prodPasswordBase64 = process.env.BEACUKAI_PROD_PASSWORD_BASE64;

    if (devPasswordBase64) {
      try {
        devPassword = Buffer.from(devPasswordBase64, 'base64').toString('utf-8');
        console.log('🔓 Using base64 decoded dev password');
      } catch (e) {
        console.log('⚠️ Failed to decode base64 dev password, using plain text');
      }
    }

    if (prodPasswordBase64) {
      try {
        prodPassword = Buffer.from(prodPasswordBase64, 'base64').toString('utf-8');
        console.log('🔓 Using base64 decoded prod password');
      } catch (e) {
        console.log('⚠️ Failed to decode base64 prod password, using plain text');
      }
    }

    this.credentials = {
      username: isDev ? (process.env.BEACUKAI_DEV_USERNAME || 'virtusDev') : (process.env.BEACUKAI_PROD_USERNAME || ''),
      password: isDev ? devPassword : prodPassword,
      appId: isDev ? (process.env.BEACUKAI_DEV_APP_ID || '56c566b2-53ab-4335-8f13-efdbe144ba52') : (process.env.BEACUKAI_PROD_APP_ID || '')
    };

    console.log('🔐 BeacukaiSessionService initialized');
    console.log(`📍 Environment: ${environment}`);
    console.log(`📍 Base URL: ${this.baseUrl}`);
    console.log(`👤 Username: ${this.credentials.username}`);
    console.log(`🆔 App ID: ${this.credentials.appId}`);

    // Debug: Log environment variables and password length
    console.log('🔍 Environment Variables Debug:');
    console.log(`   BEACUKAI_ENVIRONMENT: "${process.env.BEACUKAI_ENVIRONMENT}"`);
    console.log(`   BEACUKAI_DEV_USERNAME: "${process.env.BEACUKAI_DEV_USERNAME}"`);
    console.log(`   BEACUKAI_DEV_PASSWORD: "${process.env.BEACUKAI_DEV_PASSWORD ? process.env.BEACUKAI_DEV_PASSWORD.substring(0, 3) + '***' : 'undefined'}"`);
    console.log(`   BEACUKAI_DEV_APP_ID: "${process.env.BEACUKAI_DEV_APP_ID}"`);
    console.log(`   BEACUKAI_DEV_BASE_URL: "${process.env.BEACUKAI_DEV_BASE_URL}"`);
    console.log(`🔑 Final password length: ${this.credentials.password.length} characters`);
  }

  /**
   * Get valid session with JWT token and cookies
   */
  async getValidSession(): Promise<BeacukaiSession> {
    // Check if current session is still valid
    if (this.currentSession && this.isSessionValid(this.currentSession)) {
      console.log('✅ Using existing valid session');
      return this.currentSession;
    }

    // Create new session
    console.log('🔄 Creating new Beacukai session...');
    this.currentSession = await this.createNewSession();
    return this.currentSession;
  }

  /**
   * Create new session with JWT token
   */
  private async createNewSession(): Promise<BeacukaiSession> {
    console.log('🚀 Creating new Beacukai session...');

    try {
      // Step 1: Get JWT token
      const jwtUrl = `${this.baseUrl}/rest/pub/apigateway/jwt/getJsonWebToken?app_id=${this.credentials.appId}`;

      console.log(`🔐 Requesting JWT token from: ${jwtUrl}`);

      const jwtResponse = await fetch(jwtUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${Buffer.from(`${this.credentials.username}:${this.credentials.password}`).toString('base64')}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'E-Seal-Monitor/1.0'
        }
      });

      if (!jwtResponse.ok) {
        throw new Error(`JWT request failed: ${jwtResponse.status} ${jwtResponse.statusText}`);
      }

      const jwtData = await jwtResponse.json() as any;
      console.log('✅ JWT Response:', jwtData);

      // API Beacukai menggunakan field 'jwt' bukan 'token'
      if (!jwtData.jwt) {
        throw new Error('No JWT token received from API');
      }

      // Extract cookies from response - properly handle multiple Set-Cookie headers
      const setCookieHeaders = jwtResponse.headers.getSetCookie?.() || [];
      const cookies = setCookieHeaders.length > 0 ? setCookieHeaders :
        (jwtResponse.headers.get('set-cookie')?.split(/,(?=\s*\w+=)/) || []);
      console.log('🍪 Received cookies:', cookies);

      // Create session object
      const session: BeacukaiSession = {
        sessionId: this.generateSessionId(),
        cookies: cookies,
        expiresAt: new Date(Date.now() + (jwtData.expires_in || 3600) * 1000), // Default 1 hour
        isValid: true
      };

      // Store JWT token in session for API calls (gunakan field 'jwt')
      (session as any).jwtToken = jwtData.jwt;

      console.log('✅ New session created successfully');
      console.log(`🕐 Session expires at: ${session.expiresAt.toISOString()}`);

      return session;

    } catch (error) {
      console.error('❌ Failed to create Beacukai session:', error);
      throw error;
    }
  }

  /**
   * Make authenticated API request using session
   */
  async makeAuthenticatedRequest(
    endpoint: string,
    method: 'GET' | 'POST' = 'GET',
    data?: any
  ): Promise<any> {
    const session = await this.getValidSession();
    const jwtToken = (session as any).jwtToken;

    if (!jwtToken) {
      throw new Error('No JWT token available in session');
    }

    // Ensure endpoint starts with /tracking-eseal for API calls
    const fullEndpoint = endpoint.startsWith('/tracking-eseal') ? endpoint : `/tracking-eseal${endpoint}`;
    const url = `${this.baseUrl}${fullEndpoint}`;
    console.log(`🌐 Making authenticated ${method} request to: ${url}`);

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${jwtToken}`,
      'User-Agent': 'E-Seal-Monitor/1.0'
    };

    // Add cookies to request - properly format cookies
    if (session.cookies.length > 0) {
      // Extract cookie name=value pairs from Set-Cookie headers
       const cookieValues = session.cookies.map(cookie => {
         const cookiePart = cookie?.split(';')[0]?.trim();
         return cookiePart;
       }).filter(Boolean);

      if (cookieValues.length > 0) {
        headers['Cookie'] = cookieValues.join('; ');
        console.log('🍪 Sending cookies:', headers['Cookie']);
      }
    }

    const options: RequestInit = {
      method,
      headers
    };

    if (method === 'POST' && data) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);

      console.log(`📡 Response: ${response.status} ${response.statusText}`);

      const responseText = await response.text();
      let responseData;

      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        console.error('❌ Failed to parse response as JSON:', responseText);
        throw new Error(`API request failed: ${response.status} - ${responseText.substring(0, 200)}`);
      }

      console.log('📡 API Response:', responseData);

      // For Beacukai API, status 209 with specific error messages are valid responses
      if (!response.ok && response.status !== 209) {
        console.error(`❌ API Error: ${response.status} ${response.statusText}`, responseText);
        const errorMessage = responseData?.message || responseData?.error || `${response.status} ${response.statusText}`;
        throw new Error(`API request failed: ${errorMessage}`);
      }

      return responseData;

    } catch (error) {
      console.error('❌ Authenticated request failed:', error);
      throw error;
    }
  }

  /**
   * Check if session is still valid
   */
  private isSessionValid(session: BeacukaiSession): boolean {
    if (!session.isValid) {
      return false;
    }

    if (session.expiresAt <= new Date()) {
      console.log('⏰ Session expired');
      return false;
    }

    return true;
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Invalidate current session
   */
  async invalidateSession(): Promise<void> {
    console.log('🗑️ Invalidating current session');
    if (this.currentSession) {
      this.currentSession.isValid = false;
    }
    this.currentSession = null;
  }

  /**
   * Get session info for debugging
   */
  getSessionInfo(): any {
    if (!this.currentSession) {
      return { status: 'No active session' };
    }

    return {
      sessionId: this.currentSession.sessionId,
      isValid: this.currentSession.isValid,
      expiresAt: this.currentSession.expiresAt.toISOString(),
      cookiesCount: this.currentSession.cookies.length,
      hasJwtToken: !!(this.currentSession as any).jwtToken
    };
  }

  /**
   * Test session connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('🧪 Testing Beacukai session connectivity...');

      // Try to create a session and make a simple request
      const session = await this.getValidSession();
      console.log('✅ Session created successfully');

      // You can add a simple API call here to test the session
      // For now, just return true if session creation succeeded
      return true;

    } catch (error) {
      console.error('❌ Session connectivity test failed:', error);
      return false;
    }
  }
}