export interface BeacukaiConfig {
  environment: 'development' | 'production';
  jwtUrl: string;
  credentials: {
    username: string;
    password: string;
    appId: string;
  };
  endpoints: {
    esealUrl: string;
    dokumenUrl: string;
    trackingUrl: string;
    positionUrl: string;
  };
}

export function getBeacukaiConfig(): BeacukaiConfig {
  const environment = (process.env.BEACUKAI_ENVIRONMENT || 'development') as 'development' | 'production';
  
  const isDev = environment === 'development';
  
  return {
    environment,
    jwtUrl: process.env.BEACUKAI_JWT_URL || 'https://apisdev-gw.beacukai.go.id/rest/pub/apigateway/jwt/getJsonWebToken',
    credentials: {
      username: isDev ? (process.env.BEACUKAI_DEV_USERNAME || 'virtusDev') : (process.env.BEACUKAI_PROD_USERNAME || ''),
      password: isDev ? (process.env.BEACUKAI_DEV_PASSWORD || 'aR7#pL9qZ@1m') : (process.env.BEACUKAI_PROD_PASSWORD || ''),
      appId: isDev ? (process.env.BEACUKAI_DEV_APP_ID || '56c566b2-53ab-4335-8f13-efdbe144ba52') : (process.env.BEACUKAI_PROD_APP_ID || ''),
    },
    endpoints: {
      // Berdasarkan dokumentasi ide.md
      esealUrl: isDev ? 'https://apisdev-gw.beacukai.go.id/tracking-eseal' : 'https://apis-gw.beacukai.go.id/tracking-eseal',
      dokumenUrl: isDev ? 'https://apisdev-gw.beacukai.go.id/dokumen-eseal-service' : 'https://apis-gw.beacukai.go.id/dokumen-eseal-service',
      trackingUrl: isDev ? 'https://apisdev-gw.beacukai.go.id/tracking-eseal' : 'https://apis-gw.beacukai.go.id/tracking-eseal',
      positionUrl: isDev ? 'https://apisdev-gw.beacukai.go.id/position-eseal' : 'https://apis-gw.beacukai.go.id/position-eseal',
    },
  };
}

// Validate configuration
export function validateBeacukaiConfig(config: BeacukaiConfig): void {
  const requiredFields = [
    'jwtUrl',
    'credentials.username',
    'credentials.password',
    'credentials.appId',
    'endpoints.esealUrl',
    'endpoints.dokumenUrl',
    'endpoints.trackingUrl',
    'endpoints.positionUrl'
  ];

  for (const field of requiredFields) {
    const keys = field.split('.');
    let value: any = config;
    
    for (const key of keys) {
      value = value?.[key];
    }
    
    if (!value || value.trim() === '') {
      throw new Error(`Missing required Beacukai configuration: ${field}`);
    }
  }
}
