import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { PrismaClient, type User } from "@prisma/client";
import { admin, organization } from "better-auth/plugins";
import { ac, roles } from "./lib/permissions";

const prisma = new PrismaClient();

interface CreateUserData {
  organizationId?: string;
}

export const auth: any = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  emailAndPassword: {
    enabled: true,
    hooks: {
      createUser: {
        after: async (user: User, data: CreateUserData) => {
          if (user.role === 'admin' && data.organizationId) {
            await prisma.member.create({
              data: {
                userId: user.id,
                organizationId: data.organizationId,
                role: 'admin',
              },
            });
          }
        },
      }
    }
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day (every day the session will be updated)
  },
  databaseHooks: {
    session: {
      create: {
        before: async (session) => {
          const user = await prisma.user.findUnique({
            where: { id: session.userId },
          });

          if (user?.role === 'admin') {
            const member = await prisma.member.findFirst({
              where: { userId: user.id },
            });

            if (member) {
              return {
                data: {
                  ...session,
                  activeOrganizationId: member.organizationId,
                },
              };
            }
          }
          return { data: session };
        },
      },
    },
  },
  plugins: [
    admin({
      adminRoles: ["admin", "superadmin"],
      ac,
      roles,
    }),
    organization({
      allowUserToCreateOrganization: true, // Allow all users to create organizations for now
      organizationDeletion: {
        disabled: false
      },
      ac,
      roles,
    })
  ],
  trustedOrigins: [
    "http://localhost:5173",
    "http://localhost:3000",
    "https://eseal-alpha.vercel.app",
  ],
});
