# Better Auth Configuration
BETTER_AUTH_SECRET=your-secret-key-here-minimum-32-characters
BETTER_AUTH_URL=https://your-domain.com

# Database Configuration (PostgreSQL)
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# Server Configuration
PORT=3000
NODE_ENV=production

# GPS API Configuration
GPS_API_BASE_URL=http://smartelock.net:8088
GPS_API_USERNAME=your-gps-username
GPS_API_PASSWORD=your-gps-password

# Beacukai API Configuration (Session-based)
BEACUKAI_BASE_URL=https://apisdev-gw.beacukai.go.id
BEACUKAI_USERNAME=virtusDev
BEACUKAI_PASSWORD=aR7#pL9qZ@1m
BEACUKAI_APP_ID=56c566b2-53ab-4335-8f13-efdbe144ba52

# Cron Job Configuration
POSITION_UPDATE_CRON=*/5 * * * *
